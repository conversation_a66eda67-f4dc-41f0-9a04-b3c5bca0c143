<!DOCTYPE html>
<html>
<head>
  <title>MapTiler Wind + Temperature (Fixed Boundaries)</title>
  <meta charset="utf-8" />
  <meta name="viewport" content="initial-scale=1,maximum-scale=1,user-scalable=no" />
  <script src="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.umd.min.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-sdk-js/v3.2.0/maptiler-sdk.css" rel="stylesheet" />
  <script src="https://cdn.maptiler.com/maptiler-weather/v3.0.1/maptiler-weather.umd.min.js"></script>
  <script src="https://cdn.maptiler.com/maptiler-geocoding-control/v2.1.4/maptilersdk.umd.js"></script>
  <link href="https://cdn.maptiler.com/maptiler-geocoding-control/v2.1.4/style.css" rel="stylesheet">
  <link rel="stylesheet" href="style.css">
</head>

<body>``
  <div id="time-info">
    <span id="time-text"></span>
    <button id="play-pause-bt" class="button hide">Play 3600x</button>
    <button id="toggle-temp-bt" class="button hide">Ẩn nhiệt độ</button>
    <button id="hillshade-toggle-bt" class="button hide">Ẩn bóng địa hình</button>
    <input type="range" id="time-slider" min="0" max="11" step="1">
  </div>

  <div id="variable-name">Temperature + Wind 12</div>
  <div id="pointer-data"></div>
  <div id="map"></div>

  <!-- Scene Management Controls -->
  <div id="scene-controls">
    <button class="scene-button" id="scene-1" onclick="loadScene(1)">Scene 1<div class="active-indicator"></div></button>
    <button class="scene-button" id="scene-2" onclick="loadScene(2)">Scene 2<div class="active-indicator"></div></button>
    <button class="scene-button" id="scene-3" onclick="loadScene(3)">Scene 3<div class="active-indicator"></div></button>
    <button class="scene-button" id="scene-4" onclick="loadScene(4)">Scene 4<div class="active-indicator"></div></button>
    <button class="scene-button" id="scene-5" onclick="loadScene(5)">Scene 5<div class="active-indicator"></div></button>
    <button class="scene-button save-scene-btn" id="save-scene" onclick="saveCurrentScene()">💾 Save</button>
    <button class="scene-button clear-scenes-btn" id="clear-scenes" onclick="clearAllScenes()">🗑️ Clear All</button>
  </div>

  <!-- Color scale UI -->
  <div id="color-scale-container">
    <canvas id="color-scale-canvas" width="32" height="256"></canvas>
    <div class="scale-label max" id="scale-max-label">40°C</div>
    <div class="scale-label min" id="scale-min-label">0°C</div>
  </div>

  <!-- Projection notification -->
  <div id="projection-notification" class="projection-notification">
    ⚠️ Globe 3D tốn hiệu năng GPU đáng kể!
  </div>

  <!-- Location panel -->
  <div id="location-panel" class="location-panel">
    <div class="location-panel-header">📍 Khu vực lân cận tâm viewport</div>
    <div id="location-list"></div>
  </div>

  <!-- Location marker popup -->
  <div id="location-popup" class="location-popup">
    <div id="location-popup-text"></div>
  </div>
  
  <!-- Include Timeline Manager -->
  <script src="timeline-manager.js"></script>
  
      <!-- Include Offline Scene Manager -->
    <script src="offline-scene-manager.js"></script>
    
    <!-- Location Controls -->
  <div id="location-controls">
    <div class="location-control-header">🔍 Locations</div>
    <button class="location-btn" data-location="hanoi">Hanoi</button>
    <button class="location-btn" data-location="hochiminhcity">Ho Chi Minh City</button>
    <button class="location-btn" data-location="danang">Da Nang</button>
  </div>
  <!-- Include separated managers -->
  <script src="scene-manager.js"></script>
  <script src="location-manager.js"></script>
  <script src="border-manager.js"></script>

  <!-- Main manager and controls -->
  <script src="manager.js"></script>
  <script src="wind-controls.js"></script>
  <script src="temperature-controls.js"></script>
  <script src="wind-map-controls.js"></script>
</body>
</html>