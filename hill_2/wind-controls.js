// Simplified Wind Controls Manager - Only Color and Visibility
class WindControlsManager {
  constructor() {
    this.defaultSettings = {
      slowColor: [255, 0, 0, 30],
      fastColor: [255, 255, 255, 100],
      visible: true
    };
    
    this.currentSettings = { ...this.defaultSettings };
    this.pendingSettings = { ...this.defaultSettings };
    this.onApplyCallback = null;

    // ✅ Unit management
    this.unit = this.getUnitFromStorage() || 'km/h';
    this.unitCategory = 'windSpeed';

    this.createControls();
  }

  // Initialize wind controls
  initialize(callbacks = {}) {
    this.onApplyCallback = callbacks.onApply;
    this.updateUI();
    console.log('🌪️ Simplified wind controls initialized');
  }

  createControls() {
    // Tạo container chính - điều chỉnh vị trí xuống thấp hơn
    this.container = document.createElement('div');
    this.container.id = 'wind-controls';
    this.container.style.cssText = `
      position: fixed;
      top: 328px;
      left: 20px;
      z-index: 1000;
    `;
    
    // Tạo icon toggle - style giống projection controls (nền trắng)
    this.iconButton = document.createElement('button');
    this.iconButton.innerHTML = '💨';
    this.iconButton.style.cssText = `
      width: 40px;
      height: 40px;
      background: white;
      border: 1px solid rgba(0,0,0,0.1);
      border-radius: 6px;
      color: #666;
      font-size: 18px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
    `;
    
    // Hover effect cho icon
    this.iconButton.addEventListener('mouseenter', () => {
      this.iconButton.style.background = '#f5f5f5';
      this.iconButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
    });
    
    this.iconButton.addEventListener('mouseleave', () => {
      this.iconButton.style.background = 'white';
      this.iconButton.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
    });
    
    // Click để toggle menu
    this.iconButton.addEventListener('click', () => {
      this.toggleMenu();
    });
    
    // Tạo menu popup - hiện bên phải icon
    this.menuPopup = document.createElement('div');
    this.menuPopup.style.cssText = `
      position: absolute;
      top: 0;
      left: 45px;
      background: white;
      border-radius: 8px;
      padding: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      min-width: 200px;
      display: none;
      border: 1px solid rgba(0,0,0,0.1);
    `;
    
    this.createMenuContent();
    
    // Thêm vào container
    this.container.appendChild(this.iconButton);
    this.container.appendChild(this.menuPopup);
    
    // Thêm vào trang
    document.body.appendChild(this.container);
    
    // Click outside để đóng menu
    document.addEventListener('click', (e) => {
      if (!this.container.contains(e.target)) {
        this.menuPopup.style.display = 'none';
      }
    });
  }

  createMenuContent() {
    // Show/Hide row
    const visibilityRow = document.createElement('div');
    visibilityRow.style.cssText = `
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #eee;
    `;
    
    const visibilityLabel = document.createElement('span');
    visibilityLabel.textContent = 'Wind Animation Custom Layer';
    visibilityLabel.style.cssText = `
      color: #333;
      font-size: 13px;
      font-weight: 500;
    `;
    
    this.visibilityToggle = document.createElement('button');
    this.updateVisibilityButton();
    this.visibilityToggle.style.cssText = `
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.2s;
    `;
    
    this.visibilityToggle.addEventListener('click', () => {
      this.currentSettings.visible = !this.currentSettings.visible;
      this.updateVisibilityButton();
      this.applyVisibilityChange();
    });
    
    visibilityRow.appendChild(visibilityLabel);
    visibilityRow.appendChild(this.visibilityToggle);
    
    // Colors row
    const colorsRow = document.createElement('div');
    colorsRow.style.cssText = `
      display: flex;
      align-items: center;
      gap: 8px;
    `;
    
    const colorsLabel = document.createElement('span');
    colorsLabel.textContent = 'Colors:';
    colorsLabel.style.cssText = `
      color: #333;
      font-size: 13px;
      font-weight: 500;
    `;
    
    // Slow color picker
    this.slowColorPicker = document.createElement('input');
    this.slowColorPicker.type = 'color';
    this.slowColorPicker.value = this.rgbaToHex(this.currentSettings.slowColor);
    this.slowColorPicker.style.cssText = `
      width: 30px;
      height: 24px;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
      background: transparent;
    `;
    
    this.slowColorPicker.addEventListener('change', (e) => {
      this.pendingSettings.slowColor = this.hexToRgba(e.target.value, this.currentSettings.slowColor[3]);
      this.updateApplyButton();
    });
    
    // Fast color picker  
    this.fastColorPicker = document.createElement('input');
    this.fastColorPicker.type = 'color';
    this.fastColorPicker.value = this.rgbaToHex(this.currentSettings.fastColor);
    this.fastColorPicker.style.cssText = `
      width: 30px;
      height: 24px;
      border: 1px solid #ddd;
      border-radius: 4px;
      cursor: pointer;
      background: transparent;
    `;
    
    this.fastColorPicker.addEventListener('change', (e) => {
      this.pendingSettings.fastColor = this.hexToRgba(e.target.value, this.currentSettings.fastColor[3]);
      this.updateApplyButton();
    });
    
    // Apply button
    this.applyButton = document.createElement('button');
    this.applyButton.textContent = '✓';
    this.applyButton.style.cssText = `
      padding: 4px 8px;
      background: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      opacity: 0.5;
      pointer-events: none;
      transition: all 0.2s;
    `;
    
    this.applyButton.addEventListener('click', () => {
      this.applyColorChanges();
    });
    
    colorsRow.appendChild(colorsLabel);
    colorsRow.appendChild(this.slowColorPicker);
    colorsRow.appendChild(this.fastColorPicker);
    colorsRow.appendChild(this.applyButton);
    
    this.menuPopup.appendChild(visibilityRow);
    this.menuPopup.appendChild(colorsRow);
  }

  toggleMenu() {
    const isVisible = this.menuPopup.style.display !== 'none';
    this.menuPopup.style.display = isVisible ? 'none' : 'block';
  }

  updateVisibilityButton() {
    if (this.currentSettings.visible) {
      this.visibilityToggle.textContent = '👁️ Hide';
      this.visibilityToggle.style.background = '#f44336';
      this.visibilityToggle.style.color = 'white';
    } else {
      this.visibilityToggle.textContent = '👁️ Show';
      this.visibilityToggle.style.background = '#4CAF50';
      this.visibilityToggle.style.color = 'white';
    }
  }

  updateApplyButton() {
    const hasChanges = this.hasColorChanges();
    
    if (hasChanges) {
      this.applyButton.style.opacity = '1';
      this.applyButton.style.pointerEvents = 'auto';
      this.applyButton.style.background = '#4CAF50';
    } else {
      this.applyButton.style.opacity = '0.5';
      this.applyButton.style.pointerEvents = 'none';
      this.applyButton.style.background = '#ccc';
    }
  }

  hasColorChanges() {
    return !this.arraysEqual(this.currentSettings.slowColor, this.pendingSettings.slowColor) ||
           !this.arraysEqual(this.currentSettings.fastColor, this.pendingSettings.fastColor);
  }

      applyVisibilityChange() {
        // Visibility change luôn được apply ngay lập tức
        console.log('👁️ Visibility changed to:', this.currentSettings.visible);
        if (this.onApplyCallback) {
            // ✅ Get current timeline time when showing wind animation
            let currentTime = null;
            if (this.currentSettings.visible) {
                const timeSlider = document.getElementById('time-slider');
                if (timeSlider) {
                    currentTime = parseInt(timeSlider.value);
                    console.log(`🕐 Wind animation showing at timeline: ${new Date(currentTime)}`);
                }
            }

            this.onApplyCallback({
                visible: this.currentSettings.visible,
                type: 'visibility',
                currentTime: currentTime, // ✅ Pass current timeline time
                forceReload: this.currentSettings.visible // ✅ Force reload when showing
            });
        }
    }
    
    applyColorChanges() {
        if (this.hasColorChanges()) {
            this.currentSettings.slowColor = [...this.pendingSettings.slowColor];
            this.currentSettings.fastColor = [...this.pendingSettings.fastColor];
            
            // Log để debug
            console.log('🎨 Applying color changes:', {
                slowColor: this.currentSettings.slowColor,
                fastColor: this.currentSettings.fastColor,
                visible: this.currentSettings.visible
            });
            
            this.updateApplyButton();
            
            // Chỉ apply color nếu đang visible, nếu không chỉ lưu settings
            if (this.onApplyCallback) {
                this.onApplyCallback({
                    slowColor: this.currentSettings.slowColor,
                    fastColor: this.currentSettings.fastColor,
                    visible: this.currentSettings.visible,
                    type: 'color'
                });
            }
        }
    }

  // Utility methods
  rgbaToHex(rgba) {
    const r = Math.round(rgba[0]).toString(16).padStart(2, '0');
    const g = Math.round(rgba[1]).toString(16).padStart(2, '0');
    const b = Math.round(rgba[2]).toString(16).padStart(2, '0');
    return `#${r}${g}${b}`;
  }

  hexToRgba(hex, alpha = 255) {
    const r = parseInt(hex.slice(1, 3), 16);
    const g = parseInt(hex.slice(3, 5), 16);
    const b = parseInt(hex.slice(5, 7), 16);
    return [r, g, b, alpha];
  }

  arraysEqual(a, b) {
    return a.length === b.length && a.every((val, i) => val === b[i]);
  }

  // Update UI with current pending settings
  updateUI() {
    const settings = this.pendingSettings;
    
    // Update color pickers
    this.slowColorPicker.value = this.rgbaToHex(settings.slowColor);
    this.fastColorPicker.value = this.rgbaToHex(settings.fastColor);
    
    // Update visibility button
    this.updateVisibilityButton();
  }

  // Load settings from scene state
  loadSettings(settings) {
    if (!settings) return;

    // Load both current and pending
    this.currentSettings = { 
      slowColor: settings.slowColor || this.defaultSettings.slowColor,
      fastColor: settings.fastColor || this.defaultSettings.fastColor,
      visible: settings.visible !== undefined ? settings.visible : this.defaultSettings.visible
    };
    this.pendingSettings = { ...this.currentSettings };
    
    this.updateUI();
    this.updateApplyButton();
    
    console.log('🌪️ Wind settings loaded:', this.currentSettings);
  }

  // Get current settings
  getCurrentSettings() {
    return { ...this.currentSettings };
  }

  // Sửa lỗi: thêm method onApply
  onApply(callback) {
    this.onApplyCallback = callback;
  }

  updateSettings(newSettings) {
    this.currentSettings = { ...this.currentSettings, ...newSettings };
    this.pendingSettings = { ...this.currentSettings };
    
    // Update UI
    if (this.slowColorPicker) {
      this.slowColorPicker.value = this.rgbaToHex(this.currentSettings.slowColor);
    }
    if (this.fastColorPicker) {
      this.fastColorPicker.value = this.rgbaToHex(this.currentSettings.fastColor);
    }
    if (this.visibilityToggle) {
      this.updateVisibilityButton();
    }
    this.updateApplyButton();
  }

  // ✅ Unit Management Methods
  getUnitFromStorage() {
    try {
      return localStorage.getItem('wind_speed_unit');
    } catch (e) {
      console.warn('Failed to get wind speed unit from localStorage:', e);
      return null;
    }
  }

  saveUnitToStorage() {
    try {
      localStorage.setItem('wind_speed_unit', this.unit);
    } catch (e) {
      console.warn('Failed to save wind speed unit to localStorage:', e);
    }
  }

  setUnit(newUnit) {
    const validatedUnit = window.unitManager.validateUnit(newUnit, this.unitCategory);
    if (this.unit !== validatedUnit) {
      this.unit = validatedUnit;
      this.saveUnitToStorage();

      // Emit unit change event
      window.unitManager.emit('unitChanged', this.unitCategory, this.unit);

      console.log(`💨 Wind speed unit changed to: ${this.unit}`);
    }
  }

  getUnit() {
    return this.unit;
  }

  getUnitState() {
    return {
      unit: this.unit,
      category: this.unitCategory
    };
  }

  setUnitState(state) {
    if (state && state.unit) {
      this.setUnit(state.unit);
    }
  }

  // Convert wind speed value to current unit
  convertValue(value, fromUnit = 'km/h') {
    if (!window.unitManager) return value;
    return window.unitManager.convert(value, fromUnit, this.unit, this.unitCategory);
  }

  // Cycle to next unit
  cycleUnit() {
    const nextUnit = window.unitManager.getNextUnit(this.unit, this.unitCategory);
    this.setUnit(nextUnit);
  }
}

// Global wind controls manager instance
window.windControlsManager = new WindControlsManager();