/**
 * Unit Switcher - Manages unit switching UI for weather layers
 */
class UnitSwitcher {
  constructor() {
    this.button = null;
    this.currentLayer = null;
    this.currentManager = null;
    
    this.init();
  }

  init() {
    this.button = document.getElementById('unit-switcher');
    if (!this.button) {
      console.warn('⚠️ Unit switcher button not found');
      return;
    }

    // Add click event
    this.button.addEventListener('click', () => {
      this.cycleUnit();
    });

    // Listen for layer changes
    if (window.unitManager) {
      window.unitManager.on('layerChanged', (layerType, manager) => {
        this.updateForLayer(layerType, manager);
      });
    }

    console.log('🔧 Unit switcher initialized');
  }

  // Update unit switcher for current active layer
  updateForLayer(layerType, manager) {
    this.currentLayer = layerType;
    this.currentManager = manager;
    
    if (!this.button || !manager) return;

    // Update button text and visibility
    if (manager.getUnit) {
      const unit = manager.getUnit();
      const displayName = window.unitManager.getDisplayName(unit);
      this.button.textContent = displayName;
      this.button.style.display = 'flex';
      
      // Update tooltip
      const nextUnit = window.unitManager.getNextUnit(unit, manager.unitCategory);
      const nextDisplayName = window.unitManager.getDisplayName(nextUnit);
      this.button.title = `Current: ${displayName}, Click for: ${nextDisplayName}`;
      
      console.log(`🔧 Unit switcher updated for ${layerType}: ${displayName}`);
    } else {
      // Hide button if layer doesn't support units
      this.button.style.display = 'none';
    }
  }

  // Hide unit switcher
  hide() {
    if (this.button) {
      this.button.style.display = 'none';
    }
    this.currentLayer = null;
    this.currentManager = null;
  }

  // Cycle to next unit for current layer
  cycleUnit() {
    if (!this.currentManager || !this.currentManager.cycleUnit) {
      console.warn('⚠️ No active layer manager for unit cycling');
      return;
    }

    // Cycle unit
    this.currentManager.cycleUnit();
    
    // Update button display
    if (this.currentManager.getUnit) {
      const unit = this.currentManager.getUnit();
      const displayName = window.unitManager.getDisplayName(unit);
      this.button.textContent = displayName;
      
      // Update tooltip
      const nextUnit = window.unitManager.getNextUnit(unit, this.currentManager.unitCategory);
      const nextDisplayName = window.unitManager.getDisplayName(nextUnit);
      this.button.title = `Current: ${displayName}, Click for: ${nextDisplayName}`;
      
      console.log(`🔄 Unit cycled to: ${displayName}`);
    }
  }

  // Get current active layer info
  getCurrentLayerInfo() {
    return {
      layer: this.currentLayer,
      manager: this.currentManager,
      unit: this.currentManager ? this.currentManager.getUnit() : null
    };
  }
}

// Global instance
window.unitSwitcher = new UnitSwitcher();

// Helper function to update unit switcher when layers change
function updateUnitSwitcherForActiveLayer() {
  if (!window.unitSwitcher) return;

  // Check which layer is currently visible
  let activeLayer = null;
  let activeManager = null;

  // Check temperature layer
  if (window.temperatureControlsManager && 
      window.temperatureControlsManager.getCurrentSettings().visible) {
    activeLayer = 'temperature';
    activeManager = window.temperatureControlsManager;
  }
  // Check wind map layer
  else if (window.windMapControlsManager && 
           window.windMapControlsManager.getCurrentSettings().visible) {
    activeLayer = 'windMap';
    activeManager = window.windMapControlsManager;
  }
  // Check wind animation layer
  else if (window.windControlsManager && 
           window.windControlsManager.getCurrentSettings().visible) {
    activeLayer = 'windAnimation';
    activeManager = window.windControlsManager;
  }

  if (activeLayer && activeManager) {
    window.unitSwitcher.updateForLayer(activeLayer, activeManager);
  } else {
    window.unitSwitcher.hide();
  }
}

// Export helper function
window.updateUnitSwitcherForActiveLayer = updateUnitSwitcherForActiveLayer;

console.log('🔧 Unit switcher module loaded');
