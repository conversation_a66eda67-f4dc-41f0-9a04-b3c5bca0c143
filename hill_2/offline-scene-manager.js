// Scene Management System - Complete Implementation
// Based on hill2 copy 2.html logic

class OfflineSceneManager {
    constructor() {
        // Scene Management System
        this.scenes = {};
        this.currentSceneIndex = null;
        this.selectedSceneToSave = null;
        
        // Scene loading debounce protection
        this.sceneLoadingTimeout = null;
        this.isLoadingScene = false;
        
        // References to global variables (will be set from main file)
        this.map = null;
        this.timeSlider = null;
        this.layer = null;
        this.layerBg = null;
        this.tempLayerVisible = null;
        this.hillshadeVisible = null;
        this.isPlaying = null;
        this.toggleTempButton = null;
        this.hillshadeToggleButton = null;
        this.playPauseButton = null;
        this.refreshTimeUI = null;
    }
    
    // Initialize with references from main file
    init(references) {
        this.map = references.map;
        this.timeSlider = references.timeSlider;
        this.layer = references.layer;
        this.layerBg = references.layerBg;
        this.tempLayerVisible = references.tempLayerVisible;
        this.hillshadeVisible = references.hillshadeVisible;
        this.isPlaying = references.isPlaying;
        this.toggleTempButton = references.toggleTempButton;
        this.hillshadeToggleButton = references.hillshadeToggleButton;
        this.playPauseButton = references.playPauseButton;
        this.refreshTimeUI = references.refreshTimeUI;
        
        console.log('🎬 Scene Manager initialized');
    }
    
    // Capture current scene state
    // Trong hàm getCurrentSceneState(), thêm temperatureSettings
    getCurrentSceneState() {
        // Get projection object and extract name
        let currentProjection;
        let projectionName;
        try {
            currentProjection = this.map.getProjection?.() || this.map.projection;
            if (currentProjection) {
                projectionName = currentProjection.name || currentProjection.type || JSON.stringify(currentProjection);
                console.log(`💾 Full projection object:`, currentProjection);
                console.log(`💾 Projection properties:`, Object.keys(currentProjection));
            } else {
                projectionName = 'mercator';
            }
        } catch (e) {
            projectionName = 'mercator'; // fallback
        }
        
        console.log(`💾 Saving scene with projection name: ${projectionName}`);
        
         // Lấy trạng thái thực tế của temperature layer từ UI hoặc manager
        let actualTempVisible = false;

        // Method 1: From temperatureControlsManager
        if (window.temperatureControlsManager) {
            const tempSettings = window.temperatureControlsManager.getCurrentSettings();
            actualTempVisible = tempSettings ? tempSettings.visible : false;
            console.log('🔍 Method 1 - Temperature state from temperatureControlsManager:', {
                tempSettings: tempSettings,
                actualTempVisible: actualTempVisible,
                tempLayerVisible: window.temperatureControlsManager.tempLayerVisible
            });
        }

        // Method 2: Direct from map layer (more reliable)
        if (window.temperatureControlsManager && this.map.getLayer(window.temperatureControlsManager.TEMP_LAYER_ID)) {
            const layerVisibility = this.map.getLayoutProperty(window.temperatureControlsManager.TEMP_LAYER_ID, 'visibility');
            const mapLayerVisible = layerVisibility !== 'none';
            console.log('🔍 Method 2 - Temperature state from map layer:', {
                layerVisibility: layerVisibility,
                mapLayerVisible: mapLayerVisible
            });

            // Use map layer state as it's more reliable
            actualTempVisible = mapLayerVisible;
        }

        // Method 3: Fallback
        if (!window.temperatureControlsManager) {
            actualTempVisible = typeof this.tempLayerVisible === 'function' ? this.tempLayerVisible() : this.tempLayerVisible;
            console.log('🔍 Method 3 - Temperature state from fallback:', actualTempVisible);
        }
        
        return {
            // Timeline
            time: parseInt(this.timeSlider.value),
            
            // Viewport
            center: this.map.getCenter(),
            zoom: this.map.getZoom(),
            bearing: this.map.getBearing(),
            pitch: this.map.getPitch(),
            
            // Layers - FIX: Call functions to get actual values
            tempLayerVisible: actualTempVisible, // Sử dụng trạng thái thực tế
            hillshadeVisible: typeof this.hillshadeVisible === 'function' ? this.hillshadeVisible() : this.hillshadeVisible,
            isPlaying: typeof this.isPlaying === 'function' ? this.isPlaying() : this.isPlaying,
            
            // Additional state
            projection: projectionName,
            projectionObject: currentProjection, // Store full object too
            
            // Wind Settings - chỉ lưu trạng thái visible
            windSettings: window.windControlsManager ? {
                visible: window.windControlsManager.getCurrentSettings()?.visible || false,
                // Có thể thêm các settings khác nếu cần
                ...window.windControlsManager.getCurrentSettings()
            } : { visible: false },

            // ✅ Wind Map Settings
            windMapSettings: window.windMapControlsManager ? {
                visible: window.windMapControlsManager.getCurrentSettings()?.visible || false,
                ...window.windMapControlsManager.getCurrentSettings()
            } : { visible: false },
            
            // Temperature Settings - chỉ lưu trạng thái visible
            temperatureSettings: {
                visible: actualTempVisible
            },
    
            // Timestamp
            savedAt: new Date().toISOString()
        };
    }
    
    // Save scene to specific slot
    saveScene(sceneIndex, sceneState) {
        this.scenes[sceneIndex] = sceneState;
        
        // Update button appearance with proper CSS classes
        const button = document.getElementById(`scene-${sceneIndex}`);
        if (button) {
            // Remove all state classes first
            button.classList.remove('saved', 'empty-scene', 'short-scene', 'long-scene');
            
            // Add saved class (green background)
            button.classList.add('saved');
            button.title = `Saved: ${new Date(sceneState.savedAt).toLocaleString()}`;
        }
        
        // Save to localStorage for persistence
        localStorage.setItem('weather-app-scenes', JSON.stringify(this.scenes));
        
        console.log(`💾 Scene ${sceneIndex} saved:`, sceneState);
    }
    
    // Load scene from specific slot
    loadScene(sceneIndex) {
        // Prevent multiple rapid clicks with timeout protection
        if (this.isLoadingScene) {
            console.log(`⏳ Scene loading in progress, ignoring click`);

            // ✅ SAFETY: Force reset if stuck for too long (>5 seconds)
            const now = Date.now();
            if (!this.lastLoadingStart || (now - this.lastLoadingStart) > 5000) {
                console.log('🔧 FORCE RESET: Scene loading stuck, resetting...');
                this.isLoadingScene = false;
                if (this.sceneLoadingTimeout) {
                    clearTimeout(this.sceneLoadingTimeout);
                    this.sceneLoadingTimeout = null;
                }
            } else {
                return;
            }
        }

        // Track loading start time
        this.lastLoadingStart = Date.now();
        
        const sceneState = this.scenes[sceneIndex];
        
        if (!sceneState) {
            console.log(`❌ Scene ${sceneIndex} is empty`);
            return;
        }
        
        // Get current state for comparison
        const currentState = this.getCurrentSceneState();
        
        // Compare all metadata to check if we're actually at this scene
        const isAtSameScene = this.compareSceneStates(currentState, sceneState);
        
        if (isAtSameScene && this.currentSceneIndex === sceneIndex) {
            console.log(`📍 Already at Scene ${sceneIndex} with matching state, ignoring click`);
            return;
        }
        
        console.log(`🎬 Loading Scene ${sceneIndex}:`, sceneState);
        console.log(`🔍 Current state:`, currentState);
        
        // Set loading flag
        this.isLoadingScene = true;
        
        // Clear any existing timeout
        if (this.sceneLoadingTimeout) {
            clearTimeout(this.sceneLoadingTimeout);
        }
        
        // In the loadScene method, around line 153, replace the problematic section:
        // Update current scene indicator
        this.setActiveSceneButton(sceneIndex);
        
        // Apply timeline
        if (sceneState.time !== undefined && sceneState.time !== currentState.time) {
            console.log(`⏰ Updating timeline: ${currentState.time} → ${sceneState.time}`);
            this.timeSlider.value = sceneState.time;

            // ✅ SAFE: Check wind layer exists before calling setAnimationTime
            if (this.layer && typeof this.layer.setAnimationTime === 'function') {
                this.layer.setAnimationTime(sceneState.time / 1000);
            }

            // ✅ SAFE: Handle layerBg (could be function or object)
            if (this.layerBg) {
                const tempLayer = typeof this.layerBg === 'function' ? this.layerBg() : this.layerBg;
                if (tempLayer && typeof tempLayer.setAnimationTime === 'function') {
                    tempLayer.setAnimationTime(sceneState.time / 1000);
                }
            }

            this.refreshTimeUI();
        }
        
        // Apply viewport if different
        const viewportChanged = (
            Math.abs(currentState.center.lng - sceneState.center.lng) > 0.001 ||
            Math.abs(currentState.center.lat - sceneState.center.lat) > 0.001 ||
            Math.abs(currentState.zoom - sceneState.zoom) > 0.1 ||
            Math.abs((currentState.bearing || 0) - (sceneState.bearing || 0)) > 1 ||
            Math.abs((currentState.pitch || 0) - (sceneState.pitch || 0)) > 1
        );
        
        if (viewportChanged) {
            console.log(`🗺️ Updating viewport:`, {
                from: { center: currentState.center, zoom: currentState.zoom, bearing: currentState.bearing, pitch: currentState.pitch },
                to: { center: sceneState.center, zoom: sceneState.zoom, bearing: sceneState.bearing, pitch: sceneState.pitch }
            });
            
            this.map.easeTo({
                center: sceneState.center,
                zoom: sceneState.zoom,
                bearing: sceneState.bearing || 0,
                pitch: sceneState.pitch || 0,
                duration: 1500
            });
        }
        
        // Apply layer visibility - Get current values properly
        const currentTempVisible = typeof this.tempLayerVisible === 'function' ? this.tempLayerVisible() : this.tempLayerVisible;
        const currentHillshadeVisible = typeof this.hillshadeVisible === 'function' ? this.hillshadeVisible() : this.hillshadeVisible;
        const currentIsPlaying = typeof this.isPlaying === 'function' ? this.isPlaying() : this.isPlaying;
        
        if (sceneState.tempLayerVisible !== currentTempVisible) {
            console.log(`🌡️ Toggling temperature layer: ${currentTempVisible} → ${sceneState.tempLayerVisible}`);

            // ✅ FIXED: Sử dụng temperatureControlsManager thay vì button click
            if (window.temperatureControlsManager) {
                if (window.temperatureControlsManager.layerBg && this.map.getLayer(window.temperatureControlsManager.TEMP_LAYER_ID)) {
                    const visibility = sceneState.tempLayerVisible ? 'visible' : 'none';
                    this.map.setLayoutProperty(window.temperatureControlsManager.TEMP_LAYER_ID, 'visibility', visibility);
                    window.temperatureControlsManager.tempLayerVisible = sceneState.tempLayerVisible;
                    window.temperatureControlsManager.currentSettings.visible = sceneState.tempLayerVisible;
                    console.log(`✅ Temperature layer ${sceneState.tempLayerVisible ? 'shown' : 'hidden'} via scene manager`);
                }
            } else if (this.toggleTempButton) {
                // Fallback to button click if available
                this.toggleTempButton.click();
            } else {
                console.log('⚠️ No temperature control method available');
            }
        }
        
        if (sceneState.hillshadeVisible !== currentHillshadeVisible) {
            console.log(`🏔️ Toggling hillshade layer: ${currentHillshadeVisible} → ${sceneState.hillshadeVisible}`);
            this.hillshadeToggleButton.click();
        }
        
        // Apply playing state
        if (sceneState.isPlaying !== currentIsPlaying) {
            console.log(`▶️ Toggling play state: ${currentIsPlaying} → ${sceneState.isPlaying}`);
            this.playPauseButton.click();
        }
        
        // Apply wind settings if different
        if (sceneState.windSettings && window.windControlsManager) {
            const currentWindSettings = window.windControlsManager.getCurrentSettings();
            if (JSON.stringify(currentWindSettings) !== JSON.stringify(sceneState.windSettings)) {
                console.log(`💨 Updating wind settings:`, {
                    from: currentWindSettings,
                    to: sceneState.windSettings
                });
                
                // Load settings vào wind controls manager
                window.windControlsManager.loadSettings(sceneState.windSettings);
                
                // Apply visibility first
                if (sceneState.windSettings.visible !== undefined) {
                    const visibilitySettings = {
                        visible: sceneState.windSettings.visible,
                        type: 'visibility'
                    };
                    if (window.windControlsManager.onApplyCallback) {
                        window.windControlsManager.onApplyCallback(visibilitySettings);
                    }
                }
                
                // Apply colors if layer is visible
                if (sceneState.windSettings.visible && (sceneState.windSettings.slowColor || sceneState.windSettings.fastColor)) {
                    setTimeout(() => {
                        const colorSettings = {
                            slowColor: sceneState.windSettings.slowColor,
                            fastColor: sceneState.windSettings.fastColor,
                            visible: sceneState.windSettings.visible,
                            type: 'color'
                        };
                        if (window.windControlsManager.onApplyCallback) {
                            window.windControlsManager.onApplyCallback(colorSettings);
                        }
                    }, 100);
                }
            }
        }

        // Apply temperature settings if different
        if (sceneState.temperatureSettings && window.temperatureControlsManager) {
            const currentTempSettings = window.temperatureControlsManager.getCurrentSettings();
            const currentVisible = currentTempSettings ? currentTempSettings.visible : false;
            
            if (sceneState.temperatureSettings.visible !== currentVisible) {
                console.log(`🌡️ Updating temperature visibility: ${currentVisible} → ${sceneState.temperatureSettings.visible}`);
                
                // Load settings vào temperature controls manager
                window.temperatureControlsManager.loadSettings(sceneState.temperatureSettings);
                
                // Apply visibility
                const visibilitySettings = {
                    visible: sceneState.temperatureSettings.visible,
                    type: 'visibility'
                };
                if (window.temperatureControlsManager.onApplyCallback) {
                    window.temperatureControlsManager.onApplyCallback(visibilitySettings);
                }
            }
        }

        // ✅ Apply wind map settings if different
        if (sceneState.windMapSettings && window.windMapControlsManager) {
            const currentWindMapSettings = window.windMapControlsManager.getCurrentSettings();
            if (JSON.stringify(currentWindMapSettings) !== JSON.stringify(sceneState.windMapSettings)) {
                console.log(`🌬️ Updating wind map settings:`, {
                    from: currentWindMapSettings,
                    to: sceneState.windMapSettings
                });

                // Load settings vào wind map controls manager
                window.windMapControlsManager.loadSettings(sceneState.windMapSettings);

                // Apply visibility
                if (sceneState.windMapSettings.visible !== undefined) {
                    const visibilitySettings = {
                        visible: sceneState.windMapSettings.visible,
                        type: 'visibility'
                    };
                    if (window.windMapControlsManager.onApplyCallback) {
                        window.windMapControlsManager.onApplyCallback(visibilitySettings);
                    }
                }
            }
        }

        // Apply projection (simulate user click on projection control)
        if (sceneState.projection) {
            setTimeout(() => {
                let currentProjectionObj = this.map.getProjection?.() || this.map.projection;
                let currentProjectionName = currentProjectionObj?.name || currentProjectionObj?.type || 'mercator';
                
                if (sceneState.projection !== currentProjectionName) {
                    console.log(`🔄 Changing projection: ${currentProjectionName} → ${sceneState.projection}`);
                    
                    try {
                        // Find projection control button and simulate click
                        const projectionButton = document.querySelector('.maplibregl-ctrl-projection button, .maptiler-ctrl-projection button, button[data-projection], .projection-control button');
                        
                        if (projectionButton) {
                            projectionButton.click();
                            console.log(`✅ Projection button clicked`);
                        } else {
                            // Try to find any button containing projection-related text
                            const allButtons = document.querySelectorAll('button');
                            let foundButton = null;
                            
                            allButtons.forEach(btn => {
                                if (btn.textContent.toLowerCase().includes('globe') || 
                                    btn.textContent.toLowerCase().includes('projection') ||
                                    btn.title.toLowerCase().includes('globe') ||
                                    btn.title.toLowerCase().includes('projection')) {
                                    foundButton = btn;
                                }
                            });
                            
                            if (foundButton) {
                                foundButton.click();
                                console.log(`✅ Projection-related button clicked`);
                            } else {
                                console.log(`❌ Could not find projection control button`);
                            }
                        }
                    } catch (e) {
                        console.error('❌ Projection click simulation failed:', e);
                    }
                }
            }, 100);
        }
        
        this.currentSceneIndex = sceneIndex;
        
        // Reset loading flag after animation completes
        this.sceneLoadingTimeout = setTimeout(() => {
            this.isLoadingScene = false;
            this.lastLoadingStart = null; // Reset tracking
            console.log(`✅ Scene ${sceneIndex} loading completed`);
        }, 2000);

        // ✅ SAFETY: Backup timeout in case main timeout fails
        setTimeout(() => {
            if (this.isLoadingScene) {
                console.log('🔧 BACKUP RESET: Force clearing loading state');
                this.isLoadingScene = false;
                this.lastLoadingStart = null;
            }
        }, 5000);
    
    }
    
    // Save current scene (with selection UI)
    saveCurrentScene() {
        if (this.selectedSceneToSave) {
            // Save to selected slot
            const sceneState = this.getCurrentSceneState();
            this.saveScene(this.selectedSceneToSave, sceneState);
            this.selectedSceneToSave = null;
            this.updateSaveButtonState();
        } else {
            // Show selection mode
            this.showSceneSelection();
        }
    }
    
    // Show scene selection for saving
    // Sửa hàm showSceneSelection - bỏ nút X ở scene buttons
    showSceneSelection() {
        this.selectedSceneToSave = null;
        window.saveSelectionMode = true;
        
        // Highlight save button và thêm nút X
        const saveButton = document.getElementById('save-scene');
        saveButton.textContent = '📍 Select Slot';
        saveButton.style.background = 'rgba(255,87,34,0.9)';
        saveButton.style.position = 'relative';
        
        // Thêm nút X vào nút Save
        if (!saveButton.querySelector('.cancel-btn')) {
            const cancelBtn = document.createElement('span');
            cancelBtn.className = 'cancel-btn';
            cancelBtn.innerHTML = '✕';
            cancelBtn.style.cssText = `
                position: absolute;
                top: -5px;
                right: -5px;
                background: red;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                cursor: pointer;
                z-index: 1000;
            `;
            cancelBtn.onclick = (e) => {
                e.stopPropagation();
                this.resetSaveMode();
            };
            saveButton.appendChild(cancelBtn);
        }
        
        // Add selection styling to scene buttons (KHÔNG có nút X)
        for (let i = 1; i <= 5; i++) {
            const button = document.getElementById(`scene-${i}`);
            button.style.border = '2px solid #FF5722';
            button.style.background = 'rgba(33, 150, 243, 0.9)';
            
            button.onclick = () => {
                const defaultName = `Scene ${i}`;
                const sceneName = prompt('Nhập tên cho scene:', defaultName);
                
                if (sceneName) {
                    this.selectedSceneToSave = i;
                    
                    // Lưu ngay với tên custom
                    const sceneState = this.getCurrentSceneState();
                    sceneState.name = sceneName;
                    this.saveScene(i, sceneState);
                    
                    // Reset về trạng thái bình thường
                    this.selectedSceneToSave = null;
                    this.resetSaveMode();
                }
            };
        }
    }
    
    // Sửa hàm saveScene để cập nhật text nút ngay lập tức
    saveScene(sceneIndex, sceneState) {
        this.scenes[sceneIndex] = sceneState;
        
        // Update button appearance với tên custom
        const button = document.getElementById(`scene-${sceneIndex}`);
        if (button) {
            button.classList.remove('saved', 'empty-scene', 'short-scene', 'long-scene');
            button.classList.add('saved');
            
            // Hiển thị tên custom trên nút
            const sceneName = sceneState.name || `Scene ${sceneIndex}`;
            button.textContent = sceneName;
            button.title = `${sceneName} - Saved: ${new Date(sceneState.savedAt).toLocaleString()}`;
        }
        
        // Save to localStorage
        localStorage.setItem('weather-app-scenes', JSON.stringify(this.scenes));
        console.log(`💾 Scene ${sceneIndex} saved:`, sceneState);
    }
    
    // Sửa hàm loadSavedScenes để hiển thị tên custom
    loadSavedScenes() {
        try {
            const savedScenes = localStorage.getItem('weather-app-scenes');
            if (savedScenes) {
                this.scenes = JSON.parse(savedScenes);
                
                for (let i = 1; i <= 5; i++) {
                    const button = document.getElementById(`scene-${i}`);
                    if (button) {
                        button.classList.remove('saved', 'empty-scene', 'short-scene', 'long-scene', 'active');
                        
                        if (this.scenes[i]) {
                            button.classList.add('saved');
                            const sceneName = this.scenes[i].name || `Scene ${i}`;
                            button.textContent = sceneName; // Hiển thị tên custom
                            button.title = `${sceneName} - Saved: ${new Date(this.scenes[i].savedAt).toLocaleString()}`;
                        } else {
                            button.classList.add('empty-scene');
                            button.textContent = `Scene ${i}`; // Tên mặc định
                            button.title = 'Empty scene slot';
                        }
                    }
                }
            }
        } catch (e) {
            console.log('Error loading saved scenes:', e);
        }
    }
    
    // Sửa hàm resetSaveMode để xóa nút X khỏi Save button
    resetSaveMode() {
        const saveButton = document.getElementById('save-scene');
        saveButton.textContent = '💾 Save';
        saveButton.style.background = 'rgba(255,193,7,0.9)';
        
        // Xóa nút X khỏi Save button
        const cancelBtn = saveButton.querySelector('.cancel-btn');
        if (cancelBtn) {
            cancelBtn.remove();
        }
        
        // Reset scene buttons
        for (let i = 1; i <= 5; i++) {
            const button = document.getElementById(`scene-${i}`);
            button.style.border = 'none';
            button.style.background = '';
            button.onclick = () => { this.loadScene(i); };
        }
        
        this.loadSavedScenes(); // Khôi phục tên custom
        
        if (this.currentSceneIndex) {
            this.setActiveSceneButton(this.currentSceneIndex);
        }
        
        this.selectedSceneToSave = null;
        window.saveSelectionMode = false;
    }
    // Đảm bảo hàm setActiveSceneButton được định nghĩa đúng cách trong class
    setActiveSceneButton(sceneIndex) {
        // Remove active from all
        for (let i = 1; i <= 5; i++) {
            const button = document.getElementById(`scene-${i}`);
            if (button) {
                button.classList.remove('active');
            }
        }
        
        // Add active to current (yellow background with pulse animation)
        const activeButton = document.getElementById(`scene-${sceneIndex}`);
        if (activeButton) {
            activeButton.classList.add('active');
        }
    }
    // Get currently active layers for tile downloading
    getActiveLayers() {
        const activeLayers = [];
        
        // Add base map tiles
        activeLayers.push('basic');
        
        // Add weather layers if visible
        if (this.tempLayerVisible) {
            activeLayers.push('temperature');
        }
        
        if (this.map.getLayoutProperty(this.layer.id, 'visibility') === 'visible') {
            activeLayers.push('wind');
        }
        
        // Add terrain if visible
        if (this.hillshadeVisible) {
            activeLayers.push('terrain');
        }
        
        return activeLayers;
    }
    
    // Add both left-click and right-click handlers to scene buttons
    addContextMenusToSceneButtons() {
        console.log('🔧 Adding event handlers to scene buttons...');
        
        // Only add to scene-1 through scene-5, not save/clear buttons
        for (let i = 1; i <= 5; i++) {
            const button = document.getElementById(`scene-${i}`);
            if (button) {
                console.log(`📱 Adding handlers to: ${button.id}`);
                
                // Left-click: Load scene OR save directly if in save mode
                button.addEventListener('click', (e) => {
                    console.log(`👆 Left-click detected on: ${button.id}`);
                    
                    if (window.saveSelectionMode) {
                        // In save selection mode - save directly to this slot
                        console.log(`💾 Save mode active, saving directly to: ${button.id}`);
                        const sceneIndex = parseInt(button.id.split('-')[1]);
                        const sceneState = this.getCurrentSceneState();
                        this.saveScene(sceneIndex, sceneState);
                        this.resetSaveMode();
                    } else {
                        // Normal mode - load scene
                        this.loadScene(i);
                    }
                });
                
                // Right-click: Disable default context menu
                button.addEventListener('contextmenu', (e) => {
                    e.preventDefault();
                });
            }
        }
    }
    
    // Initialize context menus and event listeners
    initializeEventListeners() {
        console.log('🚀 Initializing scene management event listeners...');
        
        // Add context menus to scene buttons
        this.addContextMenusToSceneButtons();
        
        // ESC key to cancel save mode
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && window.saveSelectionMode) {
                console.log('⌨️ ESC pressed, canceling save mode');
                this.resetSaveMode();
            }
        });
    }
    
    // Updated clearAllScenes with enhanced functionality
    clearAllScenes() {
        // Confirmation dialog
        const confirmClear = confirm('🗑️ Xóa tất cả scenes đã lưu?\n\nHành động này không thể hoàn tác!');
        
        if (confirmClear) {
            const clearButton = document.getElementById('clear-scenes');
            const originalText = clearButton.textContent;
            
            // Show loading state
            clearButton.textContent = '🔄 Clearing...';
            clearButton.style.background = 'rgba(255,152,0,0.9)';
            clearButton.disabled = true;
            
            // Clear localStorage first
            localStorage.removeItem('weather-app-scenes');
            this.scenes = {};
            this.currentSceneIndex = null;
            
            // Clear timeline markers
            if (window.timelineManager) {
                window.timelineManager.clearAllMarkers();
            }
            
            // Reset all button appearances
            for (let i = 1; i <= 5; i++) {
                const button = document.getElementById(`scene-${i}`);
                if (button) {
                    button.classList.remove('saved', 'active', 'short-save', 'long-save');
                    button.classList.add('empty-scene');
                    button.title = '';
                    button.textContent = `Scene ${i}`;
                    button.style.border = 'none';
                    button.style.boxShadow = '';
                    
                    // Remove delete buttons if any (now they're in body with data attribute)
                    const deleteBtn = document.querySelector(`.delete-btn[data-scene-id="scene-${i}"]`);
                    if (deleteBtn) deleteBtn.remove();
                }
            }
            
            // Reset save mode if active
            if (window.saveSelectionMode) {
                this.resetSaveMode();
            }
            
            console.log('🗑️ All scenes cleared (localStorage + IndexedDB)');
            
            // Show success message
            setTimeout(() => {
                clearButton.textContent = '✅ All Cleared!';
                clearButton.style.background = 'rgba(76,175,80,0.9)';
                
                setTimeout(() => {
                    clearButton.textContent = originalText;
                    clearButton.style.background = 'rgba(244,67,54,0.9)';
                    clearButton.disabled = false;
                }, 2000);
            }, 500);
        }
    }
    
    // Compare two scene states to check if they're essentially the same
    compareSceneStates(state1, state2) {
        // Compare viewport (with tolerance for floating point precision)
        const viewportSame = (
            Math.abs(state1.center.lng - state2.center.lng) < 0.001 &&
            Math.abs(state1.center.lat - state2.center.lat) < 0.001 &&
            Math.abs(state1.zoom - state2.zoom) < 0.1 &&
            Math.abs((state1.bearing || 0) - (state2.bearing || 0)) < 1 &&
            Math.abs((state1.pitch || 0) - (state2.pitch || 0)) < 1
        );
        
        // Compare layer states
        const tempSame = state1.tempLayerVisible === state2.tempLayerVisible;
        const hillshadeSame = state1.hillshadeVisible === state2.hillshadeVisible;
        const playingSame = state1.isPlaying === state2.isPlaying;

        const layersSame = tempSame && hillshadeSame && playingSame;

        console.log('🔍 Layer comparison details:', {
            tempSame: tempSame,
            tempState1: state1.tempLayerVisible,
            tempState2: state2.tempLayerVisible,
            hillshadeSame: hillshadeSame,
            hillshadeState1: state1.hillshadeVisible,
            hillshadeState2: state2.hillshadeVisible,
            playingSame: playingSame,
            playingState1: state1.isPlaying,
            playingState2: state2.isPlaying
        });
        
        // Compare timeline
        const timelineSame = (state1.time === state2.time);
        
        // Compare projection
        const projectionSame = (state1.projection === state2.projection);
        
        // Compare wind settings
        const windSame = JSON.stringify(state1.windSettings) === JSON.stringify(state2.windSettings);

        // ✅ Compare wind map settings
        const windMapSame = JSON.stringify(state1.windMapSettings) === JSON.stringify(state2.windMapSettings);
        
        console.log(`🔍 Scene comparison:`, {
            viewport: viewportSame,
            layers: layersSame,
            timeline: timelineSame,
            projection: projectionSame,
            wind: windSame,
            windMap: windMapSame
        });
        
        return viewportSame && layersSame && timelineSame && projectionSame && windSame && windMapSame;
    }
}

// Global functions for HTML onclick events
let offlineSceneManager = new OfflineSceneManager();

// Functions to be called from HTML
function loadScene(sceneIndex) {
    offlineSceneManager.loadScene(sceneIndex);
}

function saveCurrentScene() {
    offlineSceneManager.saveCurrentScene();
}

function clearAllScenes() {
    offlineSceneManager.clearAllScenes();
}

function showSaveOptionsMenu(event, sceneId) {
    offlineSceneManager.showSaveOptionsMenu(event, sceneId);
}

function handleShortSave(sceneId) {
    offlineSceneManager.handleShortSave(sceneId);
}

function resetSaveMode() {
    offlineSceneManager.resetSaveMode();
}

function getActiveLayers() {
    return offlineSceneManager.getActiveLayers();
}

function addContextMenusToSceneButtons() {
    offlineSceneManager.addContextMenusToSceneButtons();
}

// Export for use in main file
if (typeof window !== 'undefined') {
    window.offlineSceneManager = offlineSceneManager;
    window.loadScene = loadScene;
    window.saveCurrentScene = saveCurrentScene;
    window.clearAllScenes = clearAllScenes;
    window.showSaveOptionsMenu = showSaveOptionsMenu;
    window.handleShortSave = handleShortSave;
    window.resetSaveMode = resetSaveMode;
    window.getActiveLayers = getActiveLayers;
    window.addContextMenusToSceneButtons = addContextMenusToSceneButtons;
}
