body {
  margin: 0;
  padding: 0;
  font-family: sans-serif;
}

#map {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  background-color: #444952;
  background-image: url('night-stars.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

#time-info {
  position: fixed;
  width: 60vw;
  bottom: 0;
  z-index: 1;
  margin: 10px;
  text-shadow: 0px 0px 5px black;
  color: white;
  font-size: 20px;
  font-weight: 500;
  text-align: center;
  left: 0;
  right: 0;
  margin: auto;
  padding: 20px;
}

#time-slider {
  width: 100%;
  height: fit-content;
  left: 0;
  right: 0;
  z-index: 1;
  filter: drop-shadow(0 0 7px #000a);
  margin-top: 10px;
  position: relative;
}

/* Timeline markers container */
#timeline-markers {
  position: absolute;
  top: 55%; /* Hạ xuống gần timeline hơn */
  left: 0;
  right: 0;
  height: 60px; /* <PERSON><PERSON><PERSON> chi<PERSON><PERSON> cao để chứa stacked markers */
  pointer-events: none;
  z-index: 10;
  overflow: visible;
}

/* Scene markers on timeline */
.timeline-marker {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  pointer-events: none;
  /* transform: translateX(-50%); - TẠM BỎ ĐỂ TEST */
  transition: all 0.2s ease;
}

.timeline-marker.short-save {
  background: #4CAF50;
  border: 2px solid white;
  box-shadow: 0 0 4px rgba(76, 175, 80, 0.8);
}

.timeline-marker.long-save {
  background: #f44336;
  border: 2px solid white;
  box-shadow: 0 0 4px rgba(244, 67, 54, 0.8);
}

.timeline-marker.localStorage-save {
  background: #2196F3;
  border: 2px solid white;
  box-shadow: 0 0 4px rgba(33, 150, 243, 0.8);
}

.timeline-marker.outside-left {
  left: -12px !important;
  width: 10px;
  height: 10px;
  top: -5px;
  transform: none;
  border-radius: 0 50% 50% 0;
}

#pointer-data {
  z-index: 1000;
  position: absolute;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  background: rgba(0, 0, 0, 0.85);
  padding: 6px 10px;
  border-radius: 4px;
  white-space: pre-line;
  pointer-events: none;
  display: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  line-height: 1.4;
  text-align: left;
}

#pointer-data::after {
  content: '';
  position: absolute;
  bottom: 15px;
  left: -6px;
  width: 0;
  height: 0;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid rgba(0, 0, 0, 0.85);
}

#variable-name {
  z-index: 1;
  position: fixed;
  font-size: 20px;
  font-weight: 500;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  color: #fff;
  text-shadow: 0px 0px 10px #0007;
}

.button {
  cursor: pointer;
  width: auto;
  padding: 8px;
  border-radius: 3px;
  margin: 10px 0 0 0;
  font-size: 12px;
  text-align: center;
  color: #fff;
  background: #3174ff;
  font-family: sans-serif;
  font-weight: bold;
}

#color-scale-container {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(0,0,0,0.15);
  border-radius: 10px;
  padding: 12px 8px;
  box-shadow: 0 2px 8px #0003;
}
#color-scale-canvas {
  display: block;
  width: 32px;
  height: 256px;
  border-radius: 8px;
  margin-bottom: 8px;
  margin-top: 8px;
}
.scale-label {
  color: #fff;
  font-size: 15px;
  font-weight: bold;
  text-shadow: 0 1px 4px #000a;
  margin: 0 0 2px 0;
  user-select: none;
}
.scale-label.min {
  margin-bottom: 4px;
}
.scale-label.max {
  margin-top: 4px;
}

/* ✅ Unit switcher button styling */
.unit-switcher {
  width: 32px;
  height: 30px;
  margin-top: 5px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  color: white;
  font-family: Arial, sans-serif;
  font-size: 11px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.unit-switcher:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.unit-switcher:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.98);
}

/* Tùy chỉnh chiều ngang của search box */
.maplibregl-ctrl-geocoder {
  width: 310px !important; /* Thay đổi chiều ngang ở đây */
  min-width: 200px !important;
}

.maplibregl-ctrl-geocoder input {
  width: 100% !important;
}

/* Scene Management UI */
#scene-controls {
  position: fixed;
  top: 20px;
  left: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

#scene-controls .scene-button {
  position: relative; /* Ensure delete buttons position correctly */
}

/* Wind Animation Controls - removed, now handled by JS */

.scene-button {
  background: rgba(49, 116, 255, 0.9);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  min-width: 80px;
  width: 80px;
  height: 32px;
  box-sizing: border-box;
  transition: all 0.2s;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.scene-button:hover {
  background: rgba(49, 116, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.scene-button.saved {
  background: rgba(76, 175, 80, 0.9);
}

.scene-button.saved:hover {
  background: rgba(76, 175, 80, 1);
}

.scene-button.active {
  background: rgba(255, 152, 0, 0.9);
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 0.9; }
  50% { opacity: 1; }
}

/* Offline Scene Indicators */
.scene-button.short-save {
  background: rgba(76, 175, 80, 0.9) !important;
  border-left: 4px solid #4CAF50;
}

.scene-button.short-save:hover {
  background: rgba(76, 175, 80, 1) !important;
}

.scene-button.long-save {
  background: rgba(244, 67, 54, 0.9) !important;
  border-left: 4px solid #f44336;
}

.scene-button.long-save:hover {
  background: rgba(244, 67, 54, 1) !important;
}

.scene-button.empty-scene {
  background: rgba(49, 116, 255, 0.9);
  color: white;
}

.scene-button.processing {
  background: rgba(255, 152, 0, 0.9) !important;
  color: white;
}

.scene-button.processing:hover {
  background: rgba(255, 152, 0, 1) !important;
}

.scene-button.short-scene {
  background: rgba(76, 175, 80, 0.9) !important;
  color: white;
}

.scene-button.short-scene:hover {
  background: rgba(76, 175, 80, 1) !important;
}

.scene-button.long-scene {
  background: rgba(33, 150, 243, 0.9) !important;
  color: white;
}

.scene-button.long-scene:hover {
  background: rgba(33, 150, 243, 1) !important;
}

.scene-button .delete-btn {
  position: fixed;
  background: #f44336;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 2px solid white;
  font-weight: bold;
  z-index: 1001;
}

.scene-button .delete-btn:hover {
  background: #d32f2f;
  transform: scale(1.1);
}

/* Active scene indicator */
.scene-button .active-indicator {
  position: absolute;
  top: 50%;
  right: -12px;
  transform: translateY(-50%);
  width: 0;
  height: 0;
  border-right: 8px solid #000000;
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  display: none;
}

.scene-button.active .active-indicator {
  display: block;
} 

/* Thêm các class cho style inline */

.hide {
  display: none !important;
}

.save-scene-btn {
  background:rgba(255,193,7,0.9) !important;
}

.clear-scenes-btn {
  background:rgba(244,67,54,0.9) !important;
}

.projection-notification {
  position:fixed;
  top:80px;
  right:15px;
  z-index:1000;
  background:rgba(255,107,107,0.95);
  color:#fff;
  padding:12px 16px;
  border-radius:8px;
  font-size:14px;
  display:none;
  max-width:250px;
  box-shadow:0 4px 12px rgba(0,0,0,0.3);
  border:1px solid #ff6b6b;
}

.location-panel {
  position:fixed;
  top:328px;
  right:110px;
  z-index:900;
  background:rgba(255,255,255,0.95);
  color:#333;
  padding:12px;
  border-radius:10px;
  font-size:14px;
  min-width:180px;
  max-width:220px;
  box-shadow:0 4px 16px rgba(0,0,0,0.2);
  border:1px solid #ddd;
  display:none;
}

.location-panel-header {
  font-weight:bold;
  margin-bottom:8px;
  color:#666;
  font-size:12px;
}

.location-popup {
  position:absolute;
  background:rgba(255,255,255,0.95);
  color:#333;
  padding:8px 12px;
  border-radius:8px;
  font-size:14px;
  box-shadow:0 2px 8px rgba(0,0,0,0.3);
  border:1px solid #ddd;
  display:none;
  z-index:1000;
  max-width:200px;
  white-space:nowrap;
  overflow:hidden;
  text-overflow:ellipsis;
} 