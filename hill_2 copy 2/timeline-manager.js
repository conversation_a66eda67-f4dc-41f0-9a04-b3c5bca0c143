/**
 * Timeline Manager - Fixed Timeline with Scene Markers
 */
class TimelineManager {
  constructor() {
    this.fixedTimelineStart = null;
    this.fixedTimelineEnd = null;
    this.fixedTimelineCurrent = null;
    this.sceneMarkers = [];
    
    // Timeline constants
    this.HOURS_BEFORE = 3; // 3 hours ago
    this.HOURS_AFTER = 48; // 48 hours future (2 days)
    
    this.timeSlider = null;
    this.timeTextDiv = null;
    this.markersContainer = null;
    
    this.init();
  }

  init() {
    // Get DOM elements
    this.timeSlider = document.getElementById('time-slider');
    this.timeTextDiv = document.getElementById('time-text');
    
    // Create markers container
    this.createMarkersContainer();
    
    console.log('⏰ Timeline Manager initialized');
  }

  createMarkersContainer() {
    // Create markers container if not exists
    if (!document.getElementById('timeline-markers')) {
      const markersDiv = document.createElement('div');
      markersDiv.id = 'timeline-markers';
      
      // Insert before time slider
      this.timeSlider.parentNode.insertBefore(markersDiv, this.timeSlider);
      this.markersContainer = markersDiv;
      
      console.log('📍 Timeline markers container created');
    } else {
      this.markersContainer = document.getElementById('timeline-markers');
    }
  }

  initializeFixedTimeline() {
    const now = Date.now();
    const hoursBeforeMs = this.HOURS_BEFORE * 60 * 60 * 1000;
    const hoursAfterMs = this.HOURS_AFTER * 60 * 60 * 1000;
    
    this.fixedTimelineStart = now - hoursBeforeMs;
    this.fixedTimelineEnd = now + hoursAfterMs;
    this.fixedTimelineCurrent = now;
    
    // Set timeline range to fixed range
    this.timeSlider.min = this.fixedTimelineStart;
    this.timeSlider.max = this.fixedTimelineEnd;
    this.timeSlider.value = now;
    
    this.refreshTimeUI();
    this.updateMarkersDisplay();
    
    console.log('🕐 Fixed timeline initialized:', {
      start: new Date(this.fixedTimelineStart),
      current: new Date(this.fixedTimelineCurrent),
      end: new Date(this.fixedTimelineEnd),
      range: `${this.HOURS_BEFORE}h ago → now → ${this.HOURS_AFTER}h future`
    });
  }

  refreshTimeUI() {
    if (this.timeTextDiv && this.timeSlider) {
      const time = parseInt(this.timeSlider.value);
      const date = new Date(time);
      this.timeTextDiv.innerText = date.toString();
    }
  }

  // Add scene marker
  addSceneMarker(sceneId, sceneType, timestamp, sceneName) {
    // Remove existing marker for this scene
    this.removeSceneMarker(sceneId);
    
    const marker = {
      sceneId: sceneId,
      type: sceneType, // 'short' or 'long'
      timestamp: timestamp,
      name: sceneName
    };
    
    this.sceneMarkers.push(marker);
    this.updateMarkersDisplay();
    
    console.log(`📍 Added ${sceneType} marker:`, {
      scene: sceneId,
      name: sceneName,
      time: new Date(timestamp)
    });
  }

  // Remove scene marker
  removeSceneMarker(sceneId) {
    this.sceneMarkers = this.sceneMarkers.filter(marker => marker.sceneId !== sceneId);
    this.updateMarkersDisplay();
    
    console.log(`🗑️ Removed marker for: ${sceneId}`);
  }

  // Clear all markers
  clearAllMarkers() {
    this.sceneMarkers = [];
    this.updateMarkersDisplay();
    console.log('🗑️ All timeline markers cleared');
  }

  // Update markers display on timeline
  updateMarkersDisplay() {
    if (!this.markersContainer) return;
    
    // Clear existing markers
    this.markersContainer.innerHTML = '';
    
    // Group markers by position to handle overlapping
    const markerGroups = this.groupMarkersByPosition();
    
    // Add markers for each group
    markerGroups.forEach(group => {
      group.markers.forEach((marker, index) => {
        this.createMarkerElement(marker, index, group.position);
      });
    });
  }

  // Group markers by their timeline position
  groupMarkersByPosition() {
    const groups = new Map();
    
    this.sceneMarkers.forEach(marker => {
      const position = this.calculateMarkerPosition(marker.timestamp);
      let positionKey;
      
      if (position.isOutside) {
        positionKey = position.type === 'left' ? 'outside-left' : 'outside-right';
      } else {
        // Round to nearest 2% to group nearby markers
        const roundedPercentage = Math.round(position.percentage / 2) * 2;
        positionKey = `inside-${roundedPercentage}`;
      }
      
      if (!groups.has(positionKey)) {
        groups.set(positionKey, {
          position: position,
          markers: []
        });
      }
      
      groups.get(positionKey).markers.push(marker);
    });
    
    return Array.from(groups.values());
  }

  createMarkerElement(marker, stackIndex, position) {
    const markerEl = document.createElement('div');
    markerEl.className = `timeline-marker ${marker.type}-save`;
    markerEl.setAttribute('data-scene-id', marker.sceneId);
    markerEl.title = `${marker.name}\n${new Date(marker.timestamp).toLocaleString()}`;
    
    // Vertical stacking - mỗi marker cách nhau 12px lên trên
    const verticalOffset = stackIndex * -12; // Âm để đi lên trên
    markerEl.style.top = `${verticalOffset - 4}px`; // -4px là vị trí gốc
    
    if (position.isOutside) {
      // Marker outside timeline (left side for old times)
      markerEl.classList.add('outside-left');
      markerEl.style.left = '-12px';
      
      console.log(`📍 ${marker.sceneId} marker outside timeline (${position.type}) stack: ${stackIndex}`);
    } else {
      // Marker inside timeline - vị trí chính xác dựa trên timeline slider
      const sliderRect = this.timeSlider.getBoundingClientRect();
      const markersRect = this.markersContainer.getBoundingClientRect();
      
      // DEBUG CSS POSITIONING
      console.log(`🎨 CSS POSITIONING DEBUG:`, {
        'slider.left': sliderRect.left,
        'slider.width': sliderRect.width,
        'markers.left': markersRect.left,
        'markers.width': markersRect.width,
        'container_offset': markersRect.left - sliderRect.left,
        'slider_position': this.timeSlider.style.position || 'static',
        'markers_position': this.markersContainer.style.position || 'static'
      });
      
      // Tính vị trí pixel dựa trên slider range và value
      const sliderMin = parseFloat(this.timeSlider.min);
      const sliderMax = parseFloat(this.timeSlider.max);
      const sliderRange = sliderMax - sliderMin;
      const valueOffset = marker.timestamp - sliderMin;
      const percentage = valueOffset / sliderRange;
      
      // Tính vị trí marker dựa trên cùng công thức với slider
      const currentValue = parseFloat(this.timeSlider.value);
      const currentOffset = currentValue - sliderMin;
      const currentPercentage = currentOffset / sliderRange;
      
      // DÙNG VỊ TRÍ TƯƠNG ĐỐI SO VỚI MARKERS CONTAINER
      const containerOffset = markersRect.left - sliderRect.left;
      const markerPercentage = (marker.timestamp - sliderMin) / sliderRange;
      
      // Vị trí pixel trong markers container (bù offset)
      const pixelPosition = markerPercentage * sliderRect.width - containerOffset;
      
      console.log(`🎯 FINAL CALCULATION:`, {
        'container_offset': containerOffset,
        'marker_percentage': markerPercentage.toFixed(4),
        'current_percentage': currentPercentage.toFixed(4),
        'pixel_position': pixelPosition.toFixed(1) + 'px',
        'should_match_current': Math.abs(marker.timestamp - currentValue) < 1000
      });
      
             // Đặt marker ở vị trí đã tính
       markerEl.style.left = `${pixelPosition}px`;
       
       console.log(`📍 ${marker.sceneId} marker placed at ${pixelPosition.toFixed(1)}px (stack: ${stackIndex})`);
    }
    
    this.markersContainer.appendChild(markerEl);
  }

  calculateMarkerPosition(timestamp) {
    const timelineRange = this.fixedTimelineEnd - this.fixedTimelineStart;
    
    if (timestamp < this.fixedTimelineStart) {
      // Outside timeline (too old)
      return { isOutside: true, type: 'left', percentage: 0 };
    } else if (timestamp > this.fixedTimelineEnd) {
      // Outside timeline (too future)
      return { isOutside: true, type: 'right', percentage: 100 };
    } else {
      // Inside timeline - tính vị trí chính xác
      const offset = timestamp - this.fixedTimelineStart;
      const percentage = (offset / timelineRange) * 100;
      
      // Đảm bảo vị trí trong khoảng 0-100%
      const clampedPercentage = Math.max(0, Math.min(100, percentage));
      
      return { isOutside: false, percentage: clampedPercentage };
    }
  }

  // Check if timestamp is within current timeline range
  isTimeInRange(timestamp) {
    return timestamp >= this.fixedTimelineStart && timestamp <= this.fixedTimelineEnd;
  }

  // Get nearest valid time within timeline range
  getNearestValidTime(timestamp) {
    if (timestamp < this.fixedTimelineStart) {
      return this.fixedTimelineStart;
    } else if (timestamp > this.fixedTimelineEnd) {
      return this.fixedTimelineEnd;
    } else {
      return timestamp;
    }
  }

  // Set timeline to specific time (for loading scenes)
  setTimelineTime(timestamp, forcePosition = false) {
    if (forcePosition || this.isTimeInRange(timestamp)) {
      // Time is in range or we're forcing it
      this.timeSlider.value = timestamp;
      this.refreshTimeUI();
      
      console.log(`⏰ Timeline set to: ${new Date(timestamp)}`);
      return timestamp;
    } else {
      // Time outside range - use nearest valid time
      const nearestTime = this.getNearestValidTime(timestamp);
      this.timeSlider.value = nearestTime;
      this.refreshTimeUI();
      
      console.log(`⚠️ Time outside range, set to nearest: ${new Date(nearestTime)}`);
      return nearestTime;
    }
  }

  // Load saved scenes and create markers
  async loadSavedScenesMarkers() {
    console.log('📦 Loading saved scenes for timeline markers...');
    
    // Clear existing markers
    this.clearAllMarkers();
    
    // Load from localStorage (old system)
    try {
      const savedScenes = localStorage.getItem('weather-app-scenes');
      if (savedScenes) {
        const scenes = JSON.parse(savedScenes);
        
        Object.entries(scenes).forEach(([sceneIndex, sceneData]) => {
          if (sceneData && sceneData.time) {
            this.addSceneMarker(
              `scene-${sceneIndex}`,
              'localStorage', // Different type for old saves
              sceneData.time,
              `Scene ${sceneIndex}`
            );
          }
        });
      }
    } catch (e) {
      console.error('Error loading localStorage scenes:', e);
    }
    
    // Load from IndexedDB (offline scene manager)
    if (window.offlineSceneManager) {
      try {
        await window.offlineSceneManager.loadTimelineMarkers(this);
      } catch (e) {
        console.error('Error loading IndexedDB scenes:', e);
      }
    }
    
    console.log(`📍 Loaded ${this.sceneMarkers.length} scene markers`);
  }

  // Get timeline info for debugging
  getTimelineInfo() {
    return {
      start: new Date(this.fixedTimelineStart),
      current: new Date(this.fixedTimelineCurrent),
      end: new Date(this.fixedTimelineEnd),
      range: `${this.HOURS_BEFORE}h ago → now → ${this.HOURS_AFTER}h future`,
      markers: this.sceneMarkers.length,
      markerDetails: this.sceneMarkers.map(m => ({
        scene: m.sceneId,
        type: m.type,
        time: new Date(m.timestamp),
        inRange: this.isTimeInRange(m.timestamp)
      }))
    };
  }
}

// Create global instance
window.timelineManager = new TimelineManager(); 