// Temperature Controls Manager with Full Temperature Functionality
class TemperatureControlsManager {
  constructor() {
    this.defaultSettings = {
      visible: true
    };
    
    this.currentSettings = { ...this.defaultSettings };
    this.onApplyCallback = null;
    
    // Temperature layer variables
    this.layerBg = null;
    this.TEMP_LAYER_ID = 'temperature-layer';
    this.tempLayerVisible = true;
    this.pointerLngLat = null;
    
    // Color scale definition - moved from manager.js
    this.colorScale = [
      // Dải màu cực lạnh: Từ trắng đến xanh nhạt
      { min: -73.3, max: -51.1, color: '#E5F0FF' },
      { min: -51.1, max: -48.3, color: '#DBE9FB' },
      { min: -48.3, max: -45.6, color: '#D3E2F7' },
      { min: -45.6, max: -42.8, color: '#CCDBF4' },
      { min: -42.8, max: -40.0, color: '#C0D4ED' },
      { min: -40.0, max: -37.2, color: '#B8CDEA' },
      { min: -37.2, max: -34.4, color: '#AFC6E7' },
      { min: -34.4, max: -31.7, color: '#A7BFE3' },
      { min: -31.7, max: -28.9, color: '#9CB8DE' },
      { min: -28.9, max: -26.1, color: '#93B1D7' },
      { min: -26.1, max: -23.3, color: '#89A5CD' },
      
      // Dải màu lạnh: Bắt đầu từ Xanh da trời nhạt, đậm dần thành Xanh dương
      { min: -23.3, max: -20.6, color:  '#04062E'},
      { min: -20.6, max: -17.8, color:  '#060A3F'},
      { min: -17.8, max: -15.0, color:  '#091057'},
      { min: -15.0, max: -12.2, color:  '#0D1871'}, // Xanh da trời
      { min: -12.2, max: -9.4, color:  '#11228B'},
      { min: -9.4, max: -6.7, color:  '#162DA6'},
      { min: -6.7, max: -3.9, color:  '#1C3AC2'},
      { min: -3.9, max: -1.1, color:  '#2348DE'}, 
      { min: -1.1, max: 1.7, color:  '#2F5BF0'},
      { min: 1.7, max: 4.4, color:  '#3B6EFF'},
      { min: 4.4, max: 7.2, color:  '#4981FF'},
      { min: 7.2, max: 10.0, color:  '#5793FF'},

      { min: 10.0, max: 12.8, color: '#66A6FF' }, // Xanh ngọc (Tile/Teal)
      { min: 12.8, max: 15.6, color:  '#A2D9D4'}, // Xanh lá cây pastel

      { min: 15.6, max: 18.3, color: '#B3E6B5' }, // Vàng chanh nhạt
      { min: 18.3, max: 21.1, color: '#FFE67F' },
      { min: 21.1, max: 22.5, color: '#FFE066' }, // Vàng tươi sáng nhất
      { min: 22.5, max: 23.9, color: '#FFD957' }, // Vàng tươi
      { min: 23.9, max: 25.2, color: '#FFD148' }, // Vàng hơi đậm
      { min: 25.2, max: 26.6, color: '#FFC83C' }, // Vàng cam nhạt
      { min: 26.6, max: 28.0, color: '#FFB82F' }, // Vàng cam
      { min: 28.0, max: 29.4, color: '#FFB52A' }, // Vàng cam đậm
      { min: 29.4, max: 30.8, color: '#FF9F1C' }, // Cam sáng
      { min: 30.8, max: 32.2, color: '#FF931A' }, // Cam
      { min: 32.2, max: 33.6, color: '#F98611' }, // Cam đậm
      { min: 33.6, max: 35.0, color: '#F67F0C' }, // Cam đỏ nhạt
      { min: 35.0, max: 36.4, color: '#F26A0A' }, // Cam đỏ
      { min: 36.4, max: 37.8, color: '#EE5808' }, // Cam đỏ đậm
      { min: 37.8, max: 40.6, color: '#E94E06' }, // Cam đỏ
      { min: 40.6, max: 43.3, color: '#DE3103' },
      { min: 43.3, max: 46.1, color: '#D21502' }, // Đỏ tươi
      { min: 46.1, max: 48.9, color: '#B70E02' },
      { min: 48.9, max: 65.6, color: '#8B0000' }  // Đỏ thẫm (Maroon)
    ];
    
    // Get DOM elements
    this.colorScaleCanvas = document.getElementById('color-scale-canvas');
    this.scaleMinLabel = document.getElementById('scale-min-label');
    this.scaleMaxLabel = document.getElementById('scale-max-label');
    this.pointerDataDiv = document.getElementById('pointer-data');
    
    this.createControls();
  }

  // Initialize temperature controls
  initialize(callbacks = {}, mapInstance) {
    this.onApplyCallback = callbacks.onApply;
    this.map = mapInstance;
    this.updateUI();
    console.log('🌡️ Temperature controls initialized');
  }

  // Temperature map initialization - moved from manager.js
  updateTemperatureColorRamp() {
    console.log('🌡️ updateTemperatureColorRamp called');
    console.log('- colorScale length:', this.colorScale?.length);
    console.log('- map exists:', !!this.map);
    console.log('- map style loaded:', this.map?.isStyleLoaded());

    if (!this.colorScale.length || !this.map) {
      console.log('❌ Early return: colorScale or map not ready');
      return;
    }
    try {
      if (this.map.getLayer(this.TEMP_LAYER_ID)) {
          this.map.removeLayer(this.TEMP_LAYER_ID);
      }
      
      const ramp = this.colorScale.map(entry => ({
          value: entry.min,
          color: this.hexToRgb(entry.color)
      }));
      
      this.layerBg = new maptilerweather.TemperatureLayer({
          id: this.TEMP_LAYER_ID,
          opacity: 1,
          colorramp: ramp,
      });
      
      // Thêm temperature layer trước hillshade
      console.log('🌡️ Adding temperature layer to map...');
      console.log('- Layer ID:', this.TEMP_LAYER_ID);
      console.log('- Before layer:', 'hillshade-shadows');
      console.log('- Hillshade layer exists:', !!this.map.getLayer('hillshade-shadows'));

      try {
        this.map.addLayer(this.layerBg, 'hillshade-shadows');
        console.log('✅ Temperature layer added successfully');
      } catch (error) {
        console.log('❌ Error adding temperature layer:', error);
        // Fallback: add without beforeId
        try {
          this.map.addLayer(this.layerBg);
          console.log('✅ Temperature layer added without beforeId');
        } catch (error2) {
          console.log('❌ Failed to add temperature layer completely:', error2);
        }
      }

      // ✅ Đảm bảo temperature layer visible từ đầu
      setTimeout(() => {
        if (this.map.getLayer(this.TEMP_LAYER_ID)) {
          this.map.setLayoutProperty(this.TEMP_LAYER_ID, 'visibility', 'visible');
          console.log('✅ Temperature layer visibility set to visible');
        }
      }, 50);

      // Thêm layer border riêng lên trên temp layer
      this.addBorderLayer();

      // Đảm bảo custom border layers luôn ở trên cùng
      setTimeout(() => {
          if (this.map.getLayer('state-boundaries')) {
              this.map.moveLayer('state-boundaries');
          }
          if (this.map.getLayer('country-boundaries')) {
              this.map.moveLayer('country-boundaries');
          }
          if (this.map.getLayer('simple-country-borders')) {
              this.map.moveLayer('simple-country-borders');
          }
          console.log('Moved border layers to top after temperature layer');
      }, 50);
      
      this.drawColorScale(ramp.map(r => r.color));
      if (this.scaleMinLabel) this.scaleMinLabel.innerText = `${this.colorScale[0].min}°C`;
      if (this.scaleMaxLabel) this.scaleMaxLabel.innerText = `${this.colorScale[this.colorScale.length-1].max}°C`;
    } catch (error) {
    console.error('❌ Error creating temperature layer:', error);
    // Retry sau một chút
    setTimeout(() => {
      if (this.map.isStyleLoaded()) {
        this.updateTemperatureColorRamp();
      }
    }, 1000);
  }
}
    

  // Add border layer - moved from manager.js
  addBorderLayer() {
    if (!this.map) return;
    
    const borderLayers = ['boundary', 'admin', 'country-border'];
    
    borderLayers.forEach(layerName => {
        try {
            if (this.map.getLayer(layerName)) {
                this.map.moveLayer(layerName, this.TEMP_LAYER_ID);
                console.log(`Moved border layer ${layerName} above temp layer`);
            }
        } catch (e) {
            console.log(`Could not move border layer ${layerName}:`, e.message);
        }
    });
  }

  // Update pointer value for temperature tooltip - moved from manager.js
  updatePointerValue(lngLat, windLayer) {
    if (!lngLat || !this.map || !this.map.isStyleLoaded()) return;
    if (!this.layerBg && this.tempLayerVisible) return;
    
    this.pointerLngLat = lngLat;
    const valueWind = windLayer ? windLayer.pickAt(lngLat.lng, lngLat.lat) : null;
    let valueTemp = null;
    if(this.layerBg) {
      valueTemp = this.layerBg.pickAt(lngLat.lng, lngLat.lat);
    }

    // Build tooltip content
    let lines = [];
    
    // Temperature line
    if (this.tempLayerVisible && valueTemp && valueTemp.value != null) {
      lines.push(`${valueTemp.value.toFixed(0)}°C`);
    }
    
    // Wind speed and direction line
    if (valueWind) {
      let windLine = `${valueWind.speedKilometersPerHour.toFixed(0)} km/h`;
      
      let directionAngle = null;
      if (valueWind.directionAngle !== undefined && valueWind.directionAngle !== null) {
        directionAngle = valueWind.directionAngle;
      } else if (valueWind.compassDirection) {
        directionAngle = this.compassToAngle(valueWind.compassDirection);
      }
      
      if (directionAngle !== null) {
        const compassText = valueWind.compassDirection || this.getCompassFromAngle(directionAngle);
        const flowAngle = (directionAngle + 270) % 360;
        windLine += ` <span style="display:inline-block;transform:rotate(${flowAngle}deg)">➤</span> ${compassText}`;
      }
      
      lines.push(windLine);
    }
    
    if (lines.length === 0) {
      lines.push("No data");
    }
    
    // Use innerHTML to support HTML styling for rotated arrow
    if (this.pointerDataDiv) {
      this.pointerDataDiv.innerHTML = lines.join('<br>');
    }
  }

  // Helper functions for wind direction
  getCompassFromAngle(degrees) {
    const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 
                       'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  }

  compassToAngle(compass) {
    const compassMap = {
      'N': 0, 'NNE': 22.5, 'NE': 45, 'ENE': 67.5,
      'E': 90, 'ESE': 112.5, 'SE': 135, 'SSE': 157.5,
      'S': 180, 'SSW': 202.5, 'SW': 225, 'WSW': 247.5,
      'W': 270, 'WNW': 292.5, 'NW': 315, 'NNW': 337.5
    };
    return compassMap[compass] || 0;
  }

  // Hex to RGB conversion - moved from manager.js
  hexToRgb(hex) {
    const v = hex.replace('#', '');
    return [parseInt(v.substring(0,2),16), parseInt(v.substring(2,4),16), parseInt(v.substring(4,6),16)];
  }

  // Draw color scale - moved from manager.js
  drawColorScale(colors) {
    if (!this.colorScaleCanvas || !colors || !Array.isArray(colors) || colors.length === 0) {
      console.warn('⚠️ Cannot draw color scale: missing canvas or colors');
      return;
    }
    
    const ctx = this.colorScaleCanvas.getContext('2d');
    ctx.clearRect(0, 0, this.colorScaleCanvas.width, this.colorScaleCanvas.height);
    for (let i = 0; i < colors.length; i++) {
      if (colors[i] && Array.isArray(colors[i]) && colors[i].length >= 3) {
        ctx.fillStyle = `rgb(${colors[i][0]},${colors[i][1]},${colors[i][2]})`;
        const y = this.colorScaleCanvas.height - (i + 1) * (this.colorScaleCanvas.height / colors.length);
        const h = this.colorScaleCanvas.height / colors.length + 1;
        ctx.fillRect(0, y, this.colorScaleCanvas.width, h);
      }
    }
  }

  // Toggle temperature layer visibility
  toggleTemperatureVisibility() {
    this.tempLayerVisible = !this.tempLayerVisible;
    
    if (this.layerBg && this.map) {
      const visibility = this.tempLayerVisible ? 'visible' : 'none';
      this.map.setLayoutProperty(this.TEMP_LAYER_ID, 'visibility', visibility);
      
      // Update UI
      this.currentSettings.visible = this.tempLayerVisible;
      this.updateUI();
      
      console.log(`🌡️ Temperature layer ${this.tempLayerVisible ? 'shown' : 'hidden'}`);
    }
  }

  // Get temperature layer for external access
  getTemperatureLayer() {
    return this.layerBg;
  }

  // Get color scale for external access
  getColorScale() {
    return this.colorScale;
  }

  createControls() {
    // Tạo container chính - đặt ngay dưới wind controls
    this.container = document.createElement('div');
    this.container.id = 'temperature-controls';
    this.container.style.cssText = `
      position: fixed;
      top: 373px;
      left: 20px;
      z-index: 1000;
    `;
    
    // Tạo icon toggle - style giống wind controls
    this.iconButton = document.createElement('button');
    this.iconButton.innerHTML = '🌡️';
    this.iconButton.style.cssText = `
      width: 40px;
      height: 40px;
      background: white;
      border: 1px solid rgba(0,0,0,0.1);
      border-radius: 6px;
      color: #666;
      font-size: 18px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
    `;
    
    // Hover effect cho icon
    this.iconButton.addEventListener('mouseenter', () => {
      this.iconButton.style.background = '#f5f5f5';
      this.iconButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
    });
    
    this.iconButton.addEventListener('mouseleave', () => {
      this.iconButton.style.background = 'white';
      this.iconButton.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
    });
    
    // Click để toggle menu
    this.iconButton.addEventListener('click', () => {
      this.toggleMenu();
    });
    
    // Tạo menu popup - hiện bên phải icon
    this.menuPopup = document.createElement('div');
    this.menuPopup.style.cssText = `
      position: absolute;
      top: 0;
      left: 45px;
      background: white;
      border-radius: 8px;
      padding: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      min-width: 200px;
      display: none;
      border: 1px solid rgba(0,0,0,0.1);
    `;
    
    this.createMenuContent();
    
    // Thêm vào container
    this.container.appendChild(this.iconButton);
    this.container.appendChild(this.menuPopup);
    
    // Thêm vào trang
    document.body.appendChild(this.container);
    
    // Click outside để đóng menu
    document.addEventListener('click', (e) => {
      if (!this.container.contains(e.target)) {
        this.menuPopup.style.display = 'none';
      }
    });
  }

  createMenuContent() {
    // Show/Hide row
    const visibilityRow = document.createElement('div');
    visibilityRow.style.cssText = `
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
    `;
    
    const visibilityLabel = document.createElement('span');
    visibilityLabel.textContent = 'Temperature Layer';
    visibilityLabel.style.cssText = `
      color: #333;
      font-size: 13px;
      font-weight: 500;
    `;
    
    this.visibilityToggle = document.createElement('button');
    this.updateVisibilityButton();
    this.visibilityToggle.style.cssText = `
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.2s;
    `;
    
    this.visibilityToggle.addEventListener('click', () => {
      this.currentSettings.visible = !this.currentSettings.visible;
      this.updateVisibilityButton();
      this.applyVisibilityChange();
    });
    
    visibilityRow.appendChild(visibilityLabel);
    visibilityRow.appendChild(this.visibilityToggle);
    
    this.menuPopup.appendChild(visibilityRow);
  }

  updateVisibilityButton() {
    if (this.currentSettings.visible) {
      this.visibilityToggle.textContent = 'Hide';
      this.visibilityToggle.style.background = '#f44336';
      this.visibilityToggle.style.color = 'white';
    } else {
      this.visibilityToggle.textContent = 'Show';
      this.visibilityToggle.style.background = '#4CAF50';
      this.visibilityToggle.style.color = 'white';
    }
  }

  toggleMenu() {
    const isVisible = this.menuPopup.style.display === 'block';
    this.menuPopup.style.display = isVisible ? 'none' : 'block';
  }

  applyVisibilityChange() {
    if (this.onApplyCallback) {
      this.onApplyCallback({
        visible: this.currentSettings.visible,
        type: 'visibility'
      });
    }
  }

  updateUI() {
    this.updateVisibilityButton();
  }

  getCurrentSettings() {
    return { ...this.currentSettings };
  }

  loadSettings(settings) {
    if (settings) {
      this.currentSettings = { ...this.defaultSettings, ...settings };
      this.updateUI();
    }
  }
}

// Export for global use
window.TemperatureControlsManager = TemperatureControlsManager;