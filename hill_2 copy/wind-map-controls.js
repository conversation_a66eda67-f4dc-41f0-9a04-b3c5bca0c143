// Wind Map Controls Manager - <PERSON><PERSON>ơng tự Temperature Controls
class WindMapControlsManager {
  constructor() {
    this.defaultSettings = {
      visible: false // Mặc định ẩn wind map
    };
    
    this.currentSettings = { ...this.defaultSettings };
    this.onApplyCallback = null;
    
    // Wind map layer variables
    this.windMapLayer = null;
    this.WIND_MAP_LAYER_ID = 'wind-map-layer';
    this.windMapVisible = false;
    
    // Wind speed color scale definition (0-40 m/s)
    this.colorScale = [
      { min: 0, max: 2, color: '#3288bd' },     // Calm - Blue
      { min: 2, max: 4, color: '#66c2a5' },     // Light air - Light green
      { min: 4, max: 6, color: '#abdda4' },     // Light breeze - Green
      { min: 6, max: 8, color: '#e6f598' },     // Gentle breeze - Light yellow
      { min: 8, max: 10, color: '#fee08b' },    // Moderate breeze - Yellow
      { min: 10, max: 12, color: '#fdae61' },   // Fresh breeze - Orange
      { min: 12, max: 14, color: '#f46d43' },   // Strong breeze - Red orange
      { min: 14, max: 17, color: '#d53e4f' },   // Near gale - Red
      { min: 17, max: 21, color: '#9e0142' },   // Gale - Dark red
      { min: 21, max: 25, color: '#67001f' },   // Strong gale - Very dark red
      { min: 25, max: 30, color: '#40004b' },   // Storm - Purple
      { min: 30, max: 40, color: '#2d004a' }    // Hurricane - Dark purple
    ];
    
    this.createControls();
  }

  // Initialize wind map controls
  initialize(callbacks = {}, mapInstance) {
    this.onApplyCallback = callbacks.onApply;
    this.map = mapInstance;
    this.updateUI();
    console.log('🌬️ Wind map controls initialized');
  }

  // Create wind map layer
  createWindMapLayer() {
    console.log('🌬️ Creating wind map layer...');
    
    if (!this.map) {
      console.error('❌ Map not available for wind map layer creation');
      return;
    }

    try {
      // Remove existing layer if any
      if (this.map.getLayer(this.WIND_MAP_LAYER_ID)) {
        this.map.removeLayer(this.WIND_MAP_LAYER_ID);
      }
      
      // Create color ramp for wind map
      const ramp = this.colorScale.map(entry => ({
        value: entry.min,
        color: this.hexToRgb(entry.color)
      }));
      
      // Create wind map layer (color-coded, not particles)
      this.windMapLayer = new maptilerweather.WindLayer({
        id: this.WIND_MAP_LAYER_ID,
        colorramp: ramp, // Use custom color ramp instead of NULL
        opacity: 1
      });
      
      // Add wind map layer to map (below wind particles)
      console.log('🌬️ Adding wind map layer to map...');
      
      // Add before wind particles layer to ensure particles are on top
      const windParticlesLayer = this.map.getLayer('Wind Particles');
      if (windParticlesLayer) {
        this.map.addLayer(this.windMapLayer, 'Wind Particles');
      } else {
        this.map.addLayer(this.windMapLayer);
      }
      
      // Set initial visibility
      setTimeout(() => {
        if (this.map.getLayer(this.WIND_MAP_LAYER_ID)) {
          const visibility = this.windMapVisible ? 'visible' : 'none';
          this.map.setLayoutProperty(this.WIND_MAP_LAYER_ID, 'visibility', visibility);
          console.log(`✅ Wind map layer visibility set to ${visibility}`);
        }
      }, 50);

      console.log('✅ Wind map layer created successfully');
      
    } catch (error) {
      console.error('❌ Error creating wind map layer:', error);
    }
  }

  // Toggle wind map layer visibility
  toggleWindMapVisibility() {
    this.windMapVisible = !this.windMapVisible;
    
    if (this.windMapLayer && this.map) {
      const visibility = this.windMapVisible ? 'visible' : 'none';
      this.map.setLayoutProperty(this.WIND_MAP_LAYER_ID, 'visibility', visibility);
      
      // Update UI
      this.currentSettings.visible = this.windMapVisible;
      this.updateUI();
      
      console.log(`🌬️ Wind map layer ${this.windMapVisible ? 'shown' : 'hidden'}`);
    }
  }

  // Get wind map layer for external access
  getWindMapLayer() {
    return this.windMapLayer;
  }

  // Get color scale for external access
  getColorScale() {
    return this.colorScale;
  }

  // Update color scale display for wind map
  updateColorScale() {
    // Get color scale canvas from temperature controls (reuse same canvas)
    const colorScaleCanvas = document.getElementById('color-scale-canvas');
    const scaleMinLabel = document.getElementById('scale-min-label');
    const scaleMaxLabel = document.getElementById('scale-max-label');

    if (colorScaleCanvas) {
      // Draw wind map color scale
      const colors = this.colorScale.map(entry => this.hexToRgb(entry.color));
      this.drawColorScale(colorScaleCanvas, colors);

      // Update labels for wind speed
      if (scaleMinLabel) scaleMinLabel.innerText = '0 m/s';
      if (scaleMaxLabel) scaleMaxLabel.innerText = '40 m/s';

      console.log('🌬️ Wind map color scale updated');
    }
  }

  // Restore temperature color scale
  restoreTemperatureColorScale() {
    if (window.temperatureControlsManager) {
      // Let temperature controls restore its color scale
      const tempColors = window.temperatureControlsManager.colorScale.map(entry =>
        window.temperatureControlsManager.hexToRgb(entry.color)
      );

      const colorScaleCanvas = document.getElementById('color-scale-canvas');
      const scaleMinLabel = document.getElementById('scale-min-label');
      const scaleMaxLabel = document.getElementById('scale-max-label');

      if (colorScaleCanvas) {
        window.temperatureControlsManager.drawColorScale(tempColors);
        if (scaleMinLabel) scaleMinLabel.innerText = `${window.temperatureControlsManager.colorScale[0].min}°C`;
        if (scaleMaxLabel) scaleMaxLabel.innerText = `${window.temperatureControlsManager.colorScale[window.temperatureControlsManager.colorScale.length-1].max}°C`;
      }

      console.log('🌡️ Temperature color scale restored');
    }
  }

  // Hex to RGB conversion
  hexToRgb(hex) {
    const v = hex.replace('#', '');
    return [parseInt(v.substring(0,2),16), parseInt(v.substring(2,4),16), parseInt(v.substring(4,6),16)];
  }

  // Draw color scale on canvas
  drawColorScale(canvas, colors) {
    if (!canvas || !colors || !Array.isArray(colors) || colors.length === 0) {
      console.warn('⚠️ Cannot draw wind map color scale: missing canvas or colors');
      return;
    }
    
    const ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    for (let i = 0; i < colors.length; i++) {
      if (colors[i] && Array.isArray(colors[i]) && colors[i].length >= 3) {
        ctx.fillStyle = `rgb(${colors[i][0]},${colors[i][1]},${colors[i][2]})`;
        const y = canvas.height - (i + 1) * (canvas.height / colors.length);
        const h = canvas.height / colors.length + 1;
        ctx.fillRect(0, y, canvas.width, h);
      }
    }
  }

  createControls() {
    // Tạo container chính - đặt ngay dưới temperature controls
    this.container = document.createElement('div');
    this.container.id = 'wind-map-controls';
    this.container.style.cssText = `
      position: fixed;
      top: 423px;
      left: 20px;
      z-index: 1000;
    `;
    
    // Tạo icon toggle - style giống temperature controls
    this.iconButton = document.createElement('button');
    this.iconButton.innerHTML = '🌬️';
    this.iconButton.style.cssText = `
      width: 40px;
      height: 40px;
      background: white;
      border: 1px solid rgba(0,0,0,0.1);
      border-radius: 6px;
      color: #666;
      font-size: 18px;
      cursor: pointer;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
      transition: all 0.2s;
      display: flex;
      align-items: center;
      justify-content: center;
    `;
    
    // Hover effect cho icon
    this.iconButton.addEventListener('mouseenter', () => {
      this.iconButton.style.background = '#f5f5f5';
      this.iconButton.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
    });
    
    this.iconButton.addEventListener('mouseleave', () => {
      this.iconButton.style.background = 'white';
      this.iconButton.style.boxShadow = '0 2px 8px rgba(0,0,0,0.15)';
    });
    
    // Click để toggle menu
    this.iconButton.addEventListener('click', () => {
      this.toggleMenu();
    });
    
    // Tạo menu popup - hiện bên phải icon
    this.menuPopup = document.createElement('div');
    this.menuPopup.style.cssText = `
      position: absolute;
      top: 0;
      left: 45px;
      background: white;
      border-radius: 8px;
      padding: 12px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.2);
      min-width: 200px;
      display: none;
      border: 1px solid rgba(0,0,0,0.1);
    `;
    
    this.createMenuContent();
    
    // Thêm vào container
    this.container.appendChild(this.iconButton);
    this.container.appendChild(this.menuPopup);
    
    // Thêm vào trang
    document.body.appendChild(this.container);
    
    // Click outside để đóng menu
    document.addEventListener('click', (e) => {
      if (!this.container.contains(e.target)) {
        this.menuPopup.style.display = 'none';
      }
    });
  }

  createMenuContent() {
    // Show/Hide row
    const visibilityRow = document.createElement('div');
    visibilityRow.style.cssText = `
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 8px;
    `;
    
    const visibilityLabel = document.createElement('span');
    visibilityLabel.textContent = 'Wind Map';
    visibilityLabel.style.cssText = `
      color: #333;
      font-size: 13px;
      font-weight: 500;
    `;
    
    this.visibilityToggle = document.createElement('button');
    this.updateVisibilityButton();
    this.visibilityToggle.style.cssText = `
      padding: 4px 8px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.2s;
    `;
    
    this.visibilityToggle.addEventListener('click', () => {
      this.currentSettings.visible = !this.currentSettings.visible;
      this.updateVisibilityButton();
      this.applyVisibilityChange();
    });
    
    visibilityRow.appendChild(visibilityLabel);
    visibilityRow.appendChild(this.visibilityToggle);
    
    this.menuPopup.appendChild(visibilityRow);
  }

  updateVisibilityButton() {
    if (this.currentSettings.visible) {
      this.visibilityToggle.textContent = 'Hide';
      this.visibilityToggle.style.background = '#f44336';
      this.visibilityToggle.style.color = 'white';
    } else {
      this.visibilityToggle.textContent = 'Show';
      this.visibilityToggle.style.background = '#4CAF50';
      this.visibilityToggle.style.color = 'white';
    }
  }

  toggleMenu() {
    const isVisible = this.menuPopup.style.display === 'block';
    this.menuPopup.style.display = isVisible ? 'none' : 'block';
  }

  applyVisibilityChange() {
    if (this.onApplyCallback) {
      this.onApplyCallback({
        visible: this.currentSettings.visible,
        type: 'visibility'
      });
    }
  }

  updateUI() {
    this.updateVisibilityButton();
  }

  getCurrentSettings() {
    return { ...this.currentSettings };
  }

  loadSettings(settings) {
    if (settings) {
      this.currentSettings = { ...this.defaultSettings, ...settings };
      this.windMapVisible = this.currentSettings.visible;
      this.updateUI();
    }
  }
}

// Export for global use
window.WindMapControlsManager = WindMapControlsManager;
