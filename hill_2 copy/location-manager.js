// === LOCATION PANEL MANAGEMENT ===
// Moved from manager.js to keep it organized

// Location panel functionality - simplified hierarchy display
let moveTimer = null;
let isMoving = false;

async function updateLocationPanel() {
  const center = map.getCenter();
  
  console.log('🎯 Getting location hierarchy for center:', center.lng.toFixed(4), center.lat.toFixed(4));
  
  try {
    // Reverse geocoding để lấy toàn bộ hierarchy như demo
    const response = await fetch(
      `https://api.maptiler.com/geocoding/${center.lng},${center.lat}.json?key=${maptilersdk.config.apiKey}&language=en`
    );
    
    if (!response.ok) {
      console.log('❌ Reverse geocoding failed:', response.status, response.statusText);
      locationList.innerHTML = '<div style="padding:8px;color:#999;">Unable to detect location</div>';
      return;
    }
    
    const data = await response.json();
    console.log('🏠 Reverse geocoding result:', data);
    
    if (!data.features || data.features.length === 0) {
      locationList.innerHTML = '<div style="padding:8px;color:#999;">No location data found</div>';
      return;
    }
    
    // Hiển thị toàn bộ hierarchy (như demo) - từ chi tiết nhất đến tổng quát nhất
    locationList.innerHTML = data.features.map(feature => {
      const placeType = feature.place_type && feature.place_type[0] ? feature.place_type[0] : 'place';
      
      return `
        <div style="padding:4px 8px;margin:2px 0;cursor:pointer;border-radius:6px;transition:all 0.2s;border:1px solid transparent;" 
             class="location-item" 
             data-bbox='${JSON.stringify(feature.bbox)}'
             data-name="${feature.place_name.replace(/"/g, '&quot;')}"
             onmouseover="this.style.background='#e3f2fd';this.style.borderColor='#90caf9';" 
             onmouseout="this.style.background='transparent';this.style.borderColor='transparent';">
          <div style="font-weight:500;color:#333;">${feature.text}</div>
          <div style="font-size:11px;color:#666;text-transform:capitalize;">${placeType}</div>
        </div>
      `;
    }).join('');
    
    // Add click events để zoom tới từng level
    document.querySelectorAll('.location-item').forEach(item => {
      item.addEventListener('click', () => {
        const bbox = JSON.parse(item.dataset.bbox);
        const name = item.dataset.name;
        
        console.log(`📍 Jumping to: ${name}, bbox:`, bbox);
        
        // Fit bounds với maxZoom để không zoom quá gần
        map.fitBounds(bbox, {
          maxZoom: 19,
          padding: 50,
          duration: 1500
        });
        
        // Tạo marker cho địa điểm được chọn
        const center = map.getCenter();
        createLocationMarker(center, name);
      });
    });
    
    locationPanel.style.display = 'block';
    console.log(`✅ Location panel updated with ${data.features.length} hierarchy levels`);
    
  } catch (error) {
    console.log('💥 Error updating location panel:', error);
    locationList.innerHTML = '<div style="padding:8px;color:#999;">Error loading locations</div>';
  }
}

// Initialize location panel events
function initializeLocationPanel() {
  // Lắng nghe sự kiện move với debounce 4 giây
  map.on('move', () => {
    isMoving = true;
    
    // Clear timer cũ
    if (moveTimer) {
      clearTimeout(moveTimer);
    }
    
    // Set timer mới - chỉ update khi dừng di chuyển > 4 giây
    moveTimer = setTimeout(() => {
      if (isMoving) {
        console.log('🕐 User stopped moving for 4s, updating location panel...');
        updateLocationPanel();
        isMoving = false;
      }
    }, 4000); // 4 giây
  });

  // Lắng nghe sự kiện zoom để cập nhật popup vị trí
  map.on('zoom', () => {
    if (currentLocationMarker && locationPopup.style.display === 'block') {
      const point = map.project(currentLocationMarker.lngLat);
      locationPopup.style.left = point.x + 'px';
      locationPopup.style.top = (point.y - 40) + 'px';
    }
  });

  // Update lần đầu khi map load xong
  map.on('load', () => {
    setTimeout(async () => {
      updateLocationPanel();
    }, 1000);
  });
  
  console.log('📍 Location panel events initialized');
}

// Export functions for use in manager.js
window.locationManager = {
  updateLocationPanel,
  initializeLocationPanel
};
