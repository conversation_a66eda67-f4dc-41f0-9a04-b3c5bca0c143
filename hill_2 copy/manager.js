// --- DOM Elements ---
const timeInfoContainer = document.getElementById("time-info");
const timeTextDiv = document.getElementById("time-text");
const timeSlider = document.getElementById("time-slider");
const playPauseButton = document.getElementById("play-pause-bt");
const pointerDataDiv = document.getElementById("pointer-data");
const colorScaleCanvas = document.getElementById('color-scale-canvas');
const scaleMinLabel = document.getElementById('scale-min-label');
const scaleMaxLabel = document.getElementById('scale-max-label');
const hillshadeToggleButton = document.getElementById('hillshade-toggle-bt');
const projectionNotification = document.getElementById('projection-notification');
const locationPanel = document.getElementById('location-panel');
const locationList = document.getElementById('location-list');
const locationPopup = document.getElementById('location-popup');
const locationPopupText = document.getElementById('location-popup-text');

// --- State Variables ---
let pointerLngLat = null;
let isPlaying = false;
let mapLoaded = false;
let colorScale = [];
let hillshadeVisible = true;
let weatherLayersAdded = false;
let locationUpdateTimeout = null;
let currentLocationMarker = null;

// Scene Management System
let scenes = {};
let currentSceneIndex = null;
let selectedSceneToSave = null;

// --- MapTiler Initialization ---
maptilersdk.config.apiKey = 'oinoatcrNmdCL1524DOl';

// Kiểm tra xem có hash trong URL không (để phân biệt lần đầu truy cập)
const hasHashInURL = window.location.hash && window.location.hash.length > 1;
const isFirstVisit = !hasHashInURL;

let mapConfig;
if (isFirstVisit) {
  // Lần đầu truy cập - zoom về Việt Nam
  mapConfig = {
    container: document.getElementById('map'),
    hash: true,
    zoom: 6, // Zoom level phù hợp để thấy toàn bộ Việt Nam
    center: [106.0, 14.0], // Tọa độ trung tâm Việt Nam
    style: maptilersdk.MapStyle.BACKDROP,
    projectionControl: true,
    projection: 'mercator'
  };
  console.log('🇻🇳 First visit detected - setting viewport to Vietnam');
} else {
  // Đã có hash - để MapTiler SDK tự xử lý hash, không đặt center/zoom
  mapConfig = {
    container: document.getElementById('map'),
    hash: true, // SDK sẽ tự động đọc từ hash
    style: maptilersdk.MapStyle.BACKDROP,
    projectionControl: true,
    projection: 'mercator'
  };
  console.log('🔄 Reload detected - letting SDK handle viewport from hash:', window.location.hash);
}

const map = new maptilersdk.Map(mapConfig);

// Thêm Geocoding Control - di chuyển vào sau khi map load
let gc;

// --- FUNCTIONS ---

// Hàm tạo location marker
function createLocationMarker(lngLat, locationName) {
    // Xóa marker cũ nếu có
    if (currentLocationMarker) {
        map.removeLayer(currentLocationMarker.id);
        map.removeSource(currentLocationMarker.id);
    }

    // Tạo marker mới
    const markerId = 'location-marker-' + Date.now();

    // Thêm source cho marker
    map.addSource(markerId, {
        type: 'geojson',
        data: {
            type: 'Feature',
            geometry: {
                type: 'Point',
                coordinates: [lngLat.lng, lngLat.lat]
            },
            properties: {
                name: locationName
            }
        }
    });

    // Thêm layer cho marker
    map.addLayer({
        id: markerId,
        type: 'circle',
        source: markerId,
        paint: {
            'circle-radius': 8,
            'circle-color': '#FF4444',
            'circle-stroke-color': '#FFFFFF',
            'circle-stroke-width': 2
        }
    });

    // Thêm layer cho icon
    map.addLayer({
        id: markerId + '-icon',
        type: 'symbol',
        source: markerId,
        layout: {
            'text-field': '📍',
            'text-size': 16,
            'text-offset': [0, -0.5]
        }
    });

    currentLocationMarker = { id: markerId, lngLat: lngLat, name: locationName };

    // Hiển thị popup
    showLocationPopup(lngLat, locationName);

    console.log(`📍 Location marker created at: ${locationName} (${lngLat.lng.toFixed(4)}, ${lngLat.lat.toFixed(4)})`);
}

// Hàm hiển thị popup
function showLocationPopup(lngLat, locationName) {
    const point = map.project(lngLat);
    locationPopupText.textContent = locationName;
    locationPopup.style.left = point.x + 'px';
    locationPopup.style.top = (point.y - 40) + 'px';
    locationPopup.style.display = 'block';

    // Tự động ẩn sau 3 giây
    setTimeout(() => {
        locationPopup.style.display = 'none';
    }, 3000);
}

// === BORDER MANAGEMENT ===
// Border management functions moved to border-manager.js

// Hàm thêm custom layers
function addCustomLayers() {
    try {
        // 1. Kiểm tra và thêm DEM source nếu chưa có
        if (!map.getSource('dem')) {
            map.addSource('dem', {
                type: 'raster-dem',
                url: `https://api.maptiler.com/tiles/terrain-rgb/tiles.json?key=${maptilersdk.config.apiKey}`,
                tileSize: 256,
                maxzoom: 12
            });
        }

        // 2. Kiểm tra và thêm hillshade layers nếu chưa có
        if (!map.getLayer('hillshade-shadows')) {
            map.addLayer({
                id: 'hillshade-shadows',
                type: 'hillshade',
                source: 'dem',
                layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                paint: {
                    'hillshade-exaggeration': 0.65,
                    'hillshade-illumination-direction': 315,
                    'hillshade-highlight-color': 'rgba(0, 0, 0, 0)',
                    'hillshade-shadow-color': '#252525'
                }
            });
        }

        if (!map.getLayer('hillshade-highlights')) {
            map.addLayer({
                id: 'hillshade-highlights',
                type: 'hillshade',
                source: 'dem',
                layout: { visibility: hillshadeVisible ? 'visible' : 'none' },
                paint: {
                    'hillshade-exaggeration': 0.65,
                    'hillshade-illumination-direction': 315,
                    'hillshade-shadow-color': 'rgba(0, 0, 0, 0)',
                    'hillshade-highlight-color': '#FFFFFF',
                    'hillshade-opacity': 0.2
                }
            });
        }

        // 3. Temperature layer will be added after controls are initialized
        // Skip for now - will be handled in initializeTemperatureControls()
        console.log('⏳ Temperature layer will be added after controls initialization...');

        // 4. Wind layer is already added in initializeWindLayer()
        // Just ensure it exists
        if (layer && !map.getLayer(layer.id)) {
            console.log('⚠️ Wind layer missing, re-adding...');
            map.addLayer(layer);
        }

        // ⚡ Giảm delay để load nhanh hơn
        setTimeout(() => {
          console.log('⚡ Fast auto-clicking hillshade button...');

          // Click 2 lần để simulate user behavior (giảm delay)
          hillshadeToggleButton.click(); // Ẩn
          setTimeout(() => {
              hillshadeToggleButton.click(); // Hiện lại
              mapLoaded = true;
              console.log('✅ Fast auto-click completed');
          }, 50); // Giảm từ 200ms → 50ms

      }, 100); // Giảm từ 500ms → 100ms


        // // 5. QUAN TRỌNG: Đảm bảo ranh giới lên trên
        // window.borderManager.ensureBoundariesOnTop();

        // console.log('All custom layers added successfully');

        // // Cuối cùng đảm bảo borders lên trên
        // setTimeout(() => {
        //     window.borderManager.ensureAllBordersOnTop();
        //     mapLoaded = true;
        // }, 300);

    } catch (error) {
        console.error('Error adding custom layers:', error);
    }
}

// === LOCATION MANAGEMENT ===
// Location panel functions moved to location-manager.js

// --- Helper Functions ---

// Hàm thêm layer border riêng
function addBorderLayer() {
    // Kiểm tra và thêm các layer border có sẵn của MapTiler
    const borderLayers = ['boundary', 'admin', 'country-border'];

    borderLayers.forEach(layerName => {
        try {
            if (map.getLayer(layerName)) {
                // Di chuyển layer border lên trên temp layer
                map.moveLayer(layerName, TEMP_LAYER_ID);
                console.log(`Moved border layer ${layerName} above temp layer`);
            }
        } catch (e) {
            console.log(`Could not move border layer ${layerName}:`, e.message);
        }
    });
}

function refreshTimeUI() {
  if (window.timelineManager) {
    window.timelineManager.refreshTimeUI();
  } else {
    // Fallback to old method
    const d = layer.getAnimationTimeDate();
    if (d) {
      timeTextDiv.innerText = d.toString();
      timeSlider.value = +d;
    }
  }
  updatePointerValue(pointerLngLat);
}

function updatePointerValue(lngLat) {
  if (temperatureControlsManager) {
    temperatureControlsManager.updatePointerValue(lngLat, layer);
  }
}

// Convert wind direction degrees to compass direction
function getCompassFromAngle(degrees) {
  const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE',
                     'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
  const index = Math.round(degrees / 22.5) % 16;
  return directions[index];
}

// Convert compass direction to angle for rotation
function compassToAngle(compass) {
  const compassMap = {
    'N': 0, 'NNE': 22.5, 'NE': 45, 'ENE': 67.5,
    'E': 90, 'ESE': 112.5, 'SE': 135, 'SSE': 157.5,
    'S': 180, 'SSW': 202.5, 'SW': 225, 'WSW': 247.5,
    'W': 270, 'WNW': 292.5, 'NW': 315, 'NNW': 337.5
  };
  return compassMap[compass] || 0;
}

function hexToRgb(hex) {
  const v = hex.replace('#', '');
  return [parseInt(v.substring(0,2),16), parseInt(v.substring(2,4),16), parseInt(v.substring(4,6),16)];
}

function drawColorScale(colors) {
  const ctx = colorScaleCanvas.getContext('2d');
  ctx.clearRect(0, 0, colorScaleCanvas.width, colorScaleCanvas.height);
  for (let i = 0; i < colors.length; i++) {
    ctx.fillStyle = `rgb(${colors[i][0]},${colors[i][1]},${colors[i][2]})`;
    const y = colorScaleCanvas.height - (i + 1) * (colorScaleCanvas.height / colors.length);
    const h = colorScaleCanvas.height / colors.length + 1;
    ctx.fillRect(0, y, colorScaleCanvas.width, h);
  }
}

// === SCENE MANAGEMENT ===
// Scene management functions moved to scene-manager.js



// Initialize simplified wind controls
function initializeWindControls() {
  console.log('🌪️ Initializing wind controls...');

  // Create new WindControlsManager instance
  window.windControlsManager = new WindControlsManager();

  // Setup callback for apply changes
  window.windControlsManager.onApply((settings) => {
    console.log('🎛️ Wind controls callback:', settings);

    if (settings.type === 'visibility') {
      // ✅ Wind animation is additive - toggle directly without affecting other layers
      toggleWindVisibility(settings.visible);
    } else if (settings.type === 'color') {
      // Color change - chỉ apply nếu đang visible
      if (settings.visible) {
        console.log('🎨 Applying colors because layer is visible');
        updateWindColors(settings);
      } else {
        console.log('💤 Layer hidden, colors saved but not applied');
        // Lưu màu nhưng không apply vì layer đang ẩn
      }
    }
  });

  console.log('✅ Wind controls initialized successfully');
}

// Initialize temperature controls
let temperatureControlsManager;

// Initialize wind map controls
let windMapControlsManager;

// ✅ WEATHER LAYER COORDINATION MANAGER
class WeatherLayerCoordinator {
  constructor() {
    this.weatherLayers = {
      temperature: null,  // Temperature layer
      windMap: null      // Wind map layer
    };
    this.windAnimation = null; // Wind animation (additive)
  }

  // Register weather layers
  registerLayers(tempManager, windMapManager, windAnimationManager) {
    this.weatherLayers.temperature = tempManager;
    this.weatherLayers.windMap = windMapManager;
    this.windAnimation = windAnimationManager;
  }

  // Hide all weather layers except the specified one
  hideOtherWeatherLayers(activeLayerType) {
    console.log(`🎛️ Hiding other weather layers, keeping: ${activeLayerType}`);

    Object.keys(this.weatherLayers).forEach(layerType => {
      if (layerType !== activeLayerType && this.weatherLayers[layerType]) {
        const manager = this.weatherLayers[layerType];

        // Hide the layer if it's currently visible
        if (manager.currentSettings && manager.currentSettings.visible) {
          console.log(`🙈 Hiding ${layerType} layer`);
          manager.currentSettings.visible = false;
          manager.updateUI();

          // Apply visibility change
          if (manager.onApplyCallback) {
            manager.onApplyCallback({
              visible: false,
              type: 'visibility'
            });
          }
        }
      }
    });
  }

  // Show a specific weather layer (and hide others)
  showWeatherLayer(layerType) {
    console.log(`🌟 Showing ${layerType} layer`);

    // First hide other weather layers
    this.hideOtherWeatherLayers(layerType);

    // Then show the requested layer
    const manager = this.weatherLayers[layerType];
    if (manager) {
      manager.currentSettings.visible = true;
      manager.updateUI();

      // ✅ Apply actual layer visibility based on layer type
      if (layerType === 'temperature') {
        if (manager.layerBg && map.getLayer(manager.TEMP_LAYER_ID)) {
          map.setLayoutProperty(manager.TEMP_LAYER_ID, 'visibility', 'visible');
          manager.tempLayerVisible = true;
          console.log(`🌡️ Temperature layer shown via coordinator`);
        }
      } else if (layerType === 'windMap') {
        if (manager.windMapLayer && map.getLayer(manager.WIND_MAP_LAYER_ID)) {
          map.setLayoutProperty(manager.WIND_MAP_LAYER_ID, 'visibility', 'visible');
          manager.windMapVisible = true;

          // ✅ Update color scale for wind map
          if (manager.updateColorScale) {
            manager.updateColorScale();
          }

          console.log(`🌬️ Wind map layer shown via coordinator`);
        }
      }
    }
  }

  // Toggle wind animation (additive, doesn't affect weather layers)
  toggleWindAnimation() {
    console.log(`💨 Toggling wind animation (additive)`);

    if (this.windAnimation) {
      this.windAnimation.currentSettings.visible = !this.windAnimation.currentSettings.visible;
      this.windAnimation.updateUI();

      // ✅ Apply actual wind layer visibility
      if (layer && map.getLayer('Wind Particles')) {
        const visibility = this.windAnimation.currentSettings.visible ? 'visible' : 'none';
        map.setLayoutProperty('Wind Particles', 'visibility', visibility);
        console.log(`💨 Wind animation ${this.windAnimation.currentSettings.visible ? 'shown' : 'hidden'} via coordinator`);
      }
    }
  }
}

// Global weather layer coordinator
let weatherCoordinator = new WeatherLayerCoordinator();

function initializeTemperatureControls() {
  console.log('🌡️ Initializing temperature controls...');

  // Create new TemperatureControlsManager instance
  temperatureControlsManager = new TemperatureControlsManager();

  // ✅ IMPORTANT: Expose globally for scene manager
  window.temperatureControlsManager = temperatureControlsManager;

  // ✅ Gán DOM elements cần thiết
  temperatureControlsManager.colorScaleCanvas = colorScaleCanvas;
  temperatureControlsManager.scaleMinLabel = scaleMinLabel;
  temperatureControlsManager.scaleMaxLabel = scaleMaxLabel;
  temperatureControlsManager.pointerDataDiv = pointerDataDiv;

  // Initialize with map instance and callbacks
  temperatureControlsManager.initialize({
    onApply: (settings) => {
      console.log('🌡️ Temperature controls callback:', settings);

      if (settings.type === 'visibility') {
        // ✅ Use weather coordinator for exclusive layer management
        if (settings.visible) {
          weatherCoordinator.showWeatherLayer('temperature');
        } else {
          // Just hide this layer without affecting others
          if (temperatureControlsManager.layerBg && map.getLayer(temperatureControlsManager.TEMP_LAYER_ID)) {
            const visibility = 'none';
            map.setLayoutProperty(temperatureControlsManager.TEMP_LAYER_ID, 'visibility', visibility);
            temperatureControlsManager.tempLayerVisible = false;
            console.log(`🌡️ Temperature layer hidden via callback`);
          }
        }
      }
    }
  }, map);

  // ✅ QUAN TRỌNG: Tạo temperature layer ngay sau khi controls được khởi tạo
  console.log('🌡️ Creating temperature layer...');
  temperatureControlsManager.updateTemperatureColorRamp();

  // ✅ Debug: Check if temperature layer was created
  setTimeout(() => {
    console.log('🔍 Debug temperature layer creation:');
    console.log('- temperatureControlsManager exists:', !!temperatureControlsManager);
    console.log('- TEMP_LAYER_ID:', temperatureControlsManager?.TEMP_LAYER_ID);
    console.log('- All map layers:', map.getStyle().layers.map(l => l.id));

    if (temperatureControlsManager && map.getLayer(temperatureControlsManager.TEMP_LAYER_ID)) {
      console.log('✅ Temperature layer exists in map');
      const visibility = map.getLayoutProperty(temperatureControlsManager.TEMP_LAYER_ID, 'visibility');
      console.log('🔍 Temperature layer visibility:', visibility);
    } else {
      console.log('❌ Temperature layer NOT found in map!');
      console.log('❌ Trying to create temperature layer again...');

      // Try to create again
      if (temperatureControlsManager) {
        temperatureControlsManager.updateTemperatureColorRamp();
      }
    }
  }, 500); // Tăng delay để đảm bảo layer được tạo

  // ✅ Đảm bảo Wind layer luôn ở trên Temperature layer
  setTimeout(() => {
    if (layer && map.getLayer(layer.id) && map.getLayer(temperatureControlsManager.TEMP_LAYER_ID)) {
      try {
        map.moveLayer(layer.id); // Move wind layer to top
        console.log('✅ Wind layer moved above temperature layer');
      } catch (e) {
        console.log('⚠️ Could not reorder layers:', e.message);
      }
    }
  }, 100);

  // ✅ Gọi drawColorScale với colors hợp lệ
  if (temperatureControlsManager.colorScale && temperatureControlsManager.colorScale.length > 0) {
    const colors = temperatureControlsManager.colorScale.map(entry =>
      temperatureControlsManager.hexToRgb(entry.color)
    );
    temperatureControlsManager.drawColorScale(colors);
    console.log('✅ Temperature layer and color scale created');
  }

  console.log('✅ Temperature controls initialized successfully');
}

// Initialize wind map controls
function initializeWindMapControls() {
  console.log('🌬️ Initializing wind map controls...');

  // Create new WindMapControlsManager instance
  windMapControlsManager = new WindMapControlsManager();

  // ✅ IMPORTANT: Expose globally for scene manager
  window.windMapControlsManager = windMapControlsManager;

  // Initialize with map instance and callbacks
  windMapControlsManager.initialize({
    onApply: (settings) => {
      console.log('🌬️ Wind map controls callback:', settings);

      if (settings.type === 'visibility') {
        // ✅ Use weather coordinator for exclusive layer management
        if (settings.visible) {
          weatherCoordinator.showWeatherLayer('windMap');
        } else {
          // Just hide this layer without affecting others
          if (windMapControlsManager.windMapLayer && map.getLayer(windMapControlsManager.WIND_MAP_LAYER_ID)) {
            const visibility = 'none';
            map.setLayoutProperty(windMapControlsManager.WIND_MAP_LAYER_ID, 'visibility', visibility);
            windMapControlsManager.windMapVisible = false;

            // ✅ Restore temperature color scale when hiding wind map
            if (windMapControlsManager.restoreTemperatureColorScale) {
              windMapControlsManager.restoreTemperatureColorScale();
            }

            console.log(`🌬️ Wind map layer hidden via callback`);
          }
        }
      }
    }
  }, map);

  // ✅ Create wind map layer after controls are initialized
  console.log('🌬️ Creating wind map layer...');
  windMapControlsManager.createWindMapLayer();

  console.log('✅ Wind map controls initialized successfully');
}

// Update wind colors by recreating layer safely
function updateWindColors(colorSettings) {
  if (!layer || !map) {
    console.error('❌ Layer not ready for color update');
    return;
  }

  try {
    console.log('🎨 Updating wind colors:', colorSettings);

    // Store current state
    const isPlaying = layer.isPlaying();
    const currentTime = layer.getAnimationTime();
    const wasVisible = map.getLayoutProperty('Wind Particles', 'visibility') === 'visible';

    // Get projection-specific settings
    const projection = map.getProjection() || 'mercator';
    const projectionSettings = windProjectionSettings[projection] || windProjectionSettings['mercator'];
    console.log(`🎨 Using ${projection} projection settings:`, projectionSettings);

    // Remove current layer
    map.removeLayer('Wind Particles');

    // Create new layer with updated colors and projection-specific settings
    const layerConfig = {
      id: "Wind Particles",
      colorramp: maptilerweather.ColorRamp.builtin.NULL,
      speed: projectionSettings.speed,
      fadeFactor: projectionSettings.fadeFactor,
      maxAmount: projectionSettings.maxAmount,
      density: projectionSettings.density,
      color: colorSettings.slowColor || [255, 0, 0, 30],
      fastColor: colorSettings.fastColor || [255, 255, 255, 100],
    };

    // Add projection-specific settings if available
    if (projectionSettings.refreshInterval) layerConfig.refreshInterval = projectionSettings.refreshInterval;
    if (projectionSettings.size) layerConfig.size = projectionSettings.size;

    layer = new maptilerweather.WindLayer(layerConfig);

    // Add new layer to map
    map.addLayer(layer);

    // Restore state when layer is ready
    layer.on("sourceReady", () => {
      if (currentTime) {
        layer.setAnimationTime(currentTime);
      }
      if (isPlaying) {
        layer.play();
      }
      if (!wasVisible) {
        map.setLayoutProperty('Wind Particles', 'visibility', 'none');
      }

      console.log('✅ Wind colors updated successfully with projection settings');
    });

    // Setup events for new layer
    setupWindLayerEvents();

  } catch (e) {
    console.error('❌ Error updating wind colors:', e);
  }
}

// Toggle wind layer visibility
function toggleWindVisibility(visible) {
  if (!map || !map.getLayer('Wind Particles')) {
    console.error('❌ Wind layer not found');
    return;
  }

  try {
    const visibility = visible ? 'visible' : 'none';
    map.setLayoutProperty('Wind Particles', 'visibility', visibility);
    console.log(`👁️ Wind layer ${visible ? 'shown' : 'hidden'}`);

    // Nếu vừa show layer và có màu đã lưu, apply màu đó
    if (visible && window.windControlsManager) {
      const currentSettings = window.windControlsManager.getCurrentSettings();
      if (currentSettings.slowColor || currentSettings.fastColor) {
        console.log('🎨 Applying saved colors after showing layer');
        updateWindColors(currentSettings);
      }
    }
  } catch (e) {
    console.error('❌ Error toggling wind visibility:', e);
  }
}

// Setup additional wind layer events (tick events)
function setupWindLayerEvents() {
  if (!layer) return;

  layer.on("tick", () => {
    if (window.timelineManager) {
      window.timelineManager.refreshTimeUI();
    }
  });

  console.log('✅ Wind layer tick events setup');
}

// Ensure both wind and temperature layers are visible and properly ordered
function ensureBothLayersVisible() {
  console.log('🔍 Ensuring both layers are visible and properly ordered...');

  // Check wind layer
  if (map.getLayer('Wind Particles')) {
    const windVisibility = map.getLayoutProperty('Wind Particles', 'visibility');
    if (windVisibility === 'none') {
      map.setLayoutProperty('Wind Particles', 'visibility', 'visible');
      console.log('✅ Wind layer made visible');
    }
  }

  // Check temperature layer
  if (temperatureControlsManager) {
    const tempLayer = temperatureControlsManager.getTemperatureLayer();
    if (tempLayer && map.getLayer(tempLayer.id)) {
      const tempVisibility = map.getLayoutProperty(tempLayer.id, 'visibility');
      if (tempVisibility === 'none') {
        map.setLayoutProperty(tempLayer.id, 'visibility', 'visible');
        console.log('✅ Temperature layer made visible');
      }
    }
  }

  // ✅ QUAN TRỌNG: Đảm bảo Wind layer luôn ở trên Temperature layer
  setTimeout(() => {
    if (layer && map.getLayer(layer.id) && temperatureControlsManager) {
      const tempLayer = temperatureControlsManager.getTemperatureLayer();
      if (tempLayer && map.getLayer(tempLayer.id)) {
        try {
          map.moveLayer(layer.id); // Move wind layer to top
          console.log('✅ Wind layer moved to top (above temperature)');
        } catch (e) {
          console.log('⚠️ Could not reorder layers:', e.message);
        }
      }
    }
  }, 50);

  console.log('✅ Both layers visibility and ordering ensured');
}

// Force show temperature layer (debug function)
function forceShowTemperatureLayer() {
  if (temperatureControlsManager && map.getLayer(temperatureControlsManager.TEMP_LAYER_ID)) {
    map.setLayoutProperty(temperatureControlsManager.TEMP_LAYER_ID, 'visibility', 'visible');
    temperatureControlsManager.tempLayerVisible = true;
    temperatureControlsManager.currentSettings.visible = true;
    console.log('🔧 FORCED temperature layer to be visible');
    return true;
  }
  console.log('❌ Cannot force show temperature layer - not found');
  return false;
}

// Debug function to check temperature state
function debugTemperatureState() {
  console.log('🔍 DEBUG Temperature State:');
  console.log('- temperatureControlsManager exists:', !!window.temperatureControlsManager);

  if (window.temperatureControlsManager) {
    console.log('- TEMP_LAYER_ID:', window.temperatureControlsManager.TEMP_LAYER_ID);
    console.log('- tempLayerVisible:', window.temperatureControlsManager.tempLayerVisible);
    console.log('- currentSettings:', window.temperatureControlsManager.getCurrentSettings());

    if (map.getLayer(window.temperatureControlsManager.TEMP_LAYER_ID)) {
      const visibility = map.getLayoutProperty(window.temperatureControlsManager.TEMP_LAYER_ID, 'visibility');
      console.log('- Map layer visibility:', visibility);
      console.log('- Map layer exists: YES');
    } else {
      console.log('- Map layer exists: NO');
    }
  }
}

// Expose debug function globally
window.debugTemperatureState = debugTemperatureState;

// ✅ PERFORMANCE TUNING FUNCTIONS
// Function to get current wind performance settings
function getWindPerformanceSettings() {
  const projection = map.getProjection() || 'mercator';
  const settings = windProjectionSettings[projection];
  console.log(`🌪️ Current wind settings for ${projection}:`, settings);
  return settings;
}

// Function to update wind performance settings (for fine-tuning)
function updateWindPerformanceSettings(projection, newSettings) {
  if (!windProjectionSettings[projection]) {
    console.error(`❌ Unknown projection: ${projection}`);
    return;
  }

  windProjectionSettings[projection] = { ...windProjectionSettings[projection], ...newSettings };
  console.log(`🎛️ Updated wind settings for ${projection}:`, windProjectionSettings[projection]);

  // If this is current projection, apply immediately
  const currentProjection = map.getProjection() || 'mercator';
  if (projection === currentProjection) {
    console.log('🔄 Applying updated settings to current layer...');
    adjustWindLayerForProjection(currentProjection);
  }
}

// Expose performance tuning functions globally
window.getWindPerformanceSettings = getWindPerformanceSettings;
window.updateWindPerformanceSettings = updateWindPerformanceSettings;

// --- Layer Initialization Flow ---
/*
🔄 INITIALIZATION FLOW (Fixed):

1️⃣ MAP LOAD (map.on('load'))
   ├── Basic map setup (water layer, geocoding control)
   ├── Set mapLoaded = true
   └── ✅ Map basic setup completed

2️⃣ MAP IDLE (map.on('idle'))
   ├── Check: !weatherLayersAdded && map.isStyleLoaded() && mapLoaded
   ├── Call: initializeWindLayer()
   │   ├── Create wind layer instance
   │   ├── Setup layer.on("sourceReady") event
   │   └── Add layer to map
   └── Call: addCustomLayers() (DEM, hillshade only - NO temperature yet)

3️⃣ WIND LAYER READY (layer.on("sourceReady"))
   ├── Call: initializeWindControls()
   ├── Call: initializeTemperatureControls()
   │   └── ✅ CREATE TEMPERATURE LAYER HERE (after controls ready)
   ├── Call: setupWindLayerEvents() (tick events)
   └── Call: initializeTimeline() (after 100ms delay)

4️⃣ TIMELINE SETUP (initializeTimeline())
   ├── Initialize timelineManager
   ├── Set initial animation time
   ├── Set temperature layer time
   └── Setup slider range

5️⃣ HILLSHADE WORKAROUND (in addCustomLayers()) - ⚡ OPTIMIZED
   ├── Auto-click hillshade button (hide) - after 100ms (was 500ms)
   ├── Auto-click hillshade button (show) - after 50ms (was 200ms)
   └── Ensures hillshade displays correctly with minimal delay

6️⃣ LAYER ORDERING FIXES
   ├── Move wind layer above temperature (3 checkpoints)
   ├── ensureBothLayersVisible() with ordering
   └── Final ordering check in initializeTimeline()

✅ All dependencies resolved in correct order
✅ No race conditions
✅ Controls initialized after layers are ready
*/

let layer; // Declare layer variable but don't initialize yet

// --- Map Events ---
map.on('load', function () {
    console.log('🗺️ Map loaded - starting initialization sequence');

    try {
        // Xử lý Water layer giống wind_temp.html
        map.setPaintProperty("Water", 'fill-color', "rgba(0, 0, 0, 0.6)");
    } catch (error) {
        console.log('Water layer styling error:', error);
    }

    // Thêm Geocoding Control sau khi map load
    gc = new maptilersdkMaptilerGeocoder.GeocodingControl({});
    map.addControl(gc, 'top-right');

    // Lắng nghe sự kiện khi user chọn địa điểm từ search box
    gc.on('result', function(e) {
        const result = e.result;
        const lngLat = result.center;
        const locationName = result.place_name || result.text;

        console.log('🔍 Search result selected:', locationName, lngLat);

        // Tạo marker cho địa điểm được chọn
        createLocationMarker(lngLat, locationName);
    });

    // Set map loaded flag
    mapLoaded = true;
    console.log('✅ Map basic setup completed');
});

// Step 2: Wait for map to be fully ready, then add all layers
map.on('idle', function() {
    if (!weatherLayersAdded && map.isStyleLoaded() && mapLoaded) {
        weatherLayersAdded = true;
        console.log('🎯 Map is idle and style loaded - initializing all layers...');

        // Initialize wind layer first
        initializeWindLayer();

        // Then add all other custom layers
        setTimeout(() => {
            addCustomLayers();
        }, 100);
    }
});

// Backup: Ensure layers are added even if idle doesn't trigger
setTimeout(() => {
    if (!weatherLayersAdded && map.isStyleLoaded() && mapLoaded) {
        weatherLayersAdded = true;
        console.log('🔄 Backup: Adding custom layers after timeout...');

        // Initialize wind layer first
        initializeWindLayer();

        // Then add all other custom layers
        addCustomLayers();
    }
}, 3000); // 3 second backup

// Event để đảm bảo ranh giới luôn hiển thị khi style thay đổi
map.on('styledata', function() {
    if (mapLoaded) {
        setTimeout(() => {
            if (window.borderManager) {
                window.borderManager.ensureBoundariesOnTop();
                window.borderManager.addCustomBorderLayers();
            }
        }, 50);
    }
});

// Step 3: Initialize wind layer and setup events
function initializeWindLayer() {
    console.log('🌪️ Initializing wind layer...');

    // Get current projection and its settings
    const projection = map.getProjection() || 'mercator';
    const projectionSettings = windProjectionSettings[projection] || windProjectionSettings['mercator'];
    console.log(`🌪️ Creating wind layer for ${projection} projection:`, projectionSettings);

    // Create wind layer with projection-specific settings
    const layerConfig = {
        id: "Wind Particles",
        colorramp: maptilerweather.ColorRamp.builtin.NULL,
        speed: projectionSettings.speed,
        fadeFactor: projectionSettings.fadeFactor,
        maxAmount: projectionSettings.maxAmount,
        density: projectionSettings.density,
        color: [255, 0, 0, 30],
        fastColor: [255, 255, 255, 100],
    };

    // Add projection-specific settings if available
    if (projectionSettings.refreshInterval) layerConfig.refreshInterval = projectionSettings.refreshInterval;
    if (projectionSettings.size) layerConfig.size = projectionSettings.size;

    layer = new maptilerweather.WindLayer(layerConfig);

    // Setup wind layer events BEFORE adding to map
    layer.on("sourceReady", () => {
        console.log('✅ Wind layer source ready - initializing controls...');

        // Initialize controls after layer is ready
        initializeWindControls();
        initializeTemperatureControls();
        initializeWindMapControls();

        // ✅ Register layers with coordinator after all are initialized
        setTimeout(() => {
          weatherCoordinator.registerLayers(
            temperatureControlsManager,  // Temperature layer
            windMapControlsManager,      // Wind map layer
            windControlsManager          // Wind animation (additive)
          );
          console.log('🎛️ Weather layer coordinator initialized');
        }, 500);

        // Setup additional events
        setupWindLayerEvents();

        // ✅ Ensure both layers are visible by default
        setTimeout(() => {
            ensureBothLayersVisible();
        }, 50);

        // Initialize timeline after controls are ready
        setTimeout(() => {
            initializeTimeline();
        }, 150);
    });

    // Add layer to map
    if (!map.getLayer(layer.id)) {
        map.addLayer(layer);
        console.log('✅ Wind layer added to map');
    }
}

// Step 4: Initialize timeline and set initial time
function initializeTimeline() {
    console.log('⏰ Initializing timeline...');

    if (window.timelineManager) {
        window.timelineManager.initializeFixedTimeline();

        // Set initial weather layer time to current
        const now = Date.now();
        layer.setAnimationTime(now / 1000);

        // Set temperature layer time if available
        if (temperatureControlsManager) {
            const tempLayer = temperatureControlsManager.getTemperatureLayer();
            if (tempLayer) tempLayer.setAnimationTime(now / 1000);
        }

        // Set wind map layer time if available
        if (windMapControlsManager) {
            const windMapLayer = windMapControlsManager.getWindMapLayer();
            if (windMapLayer) windMapLayer.setAnimationTime(now / 1000);
        }

        console.log('✅ Timeline initialized');
    }

    // Setup timeline slider range
    const startDate = layer.getAnimationStartDate();
    const endDate = layer.getAnimationEndDate();
    if (startDate && endDate) {
        timeSlider.min = +startDate;
        timeSlider.max = +endDate;
        console.log('✅ Timeline slider range set');
    }

    // ✅ Final layer ordering check + force show temperature
    setTimeout(() => {
        if (layer && map.getLayer(layer.id) && temperatureControlsManager) {
            const tempLayer = temperatureControlsManager.getTemperatureLayer();
            if (tempLayer && map.getLayer(tempLayer.id)) {
                try {
                    // Force show temperature layer first
                    forceShowTemperatureLayer();

                    // Then ensure wind is on top
                    map.moveLayer(layer.id); // Ensure wind is on top
                    console.log('✅ Final layer ordering: Wind on top, Temperature visible');
                } catch (e) {
                    console.log('⚠️ Final layer ordering failed:', e.message);
                }
            }
        }
    }, 300); // Tăng delay để đảm bảo temperature layer đã sẵn sàng
}

// --- UI Event Listeners ---
let timeSliderTimeout = null;

timeSlider.addEventListener("input", () => {
  const time = parseInt(timeSlider.value);
  
  // Hủy timeout cũ nếu user vẫn đang kéo
  if (timeSliderTimeout) {
    clearTimeout(timeSliderTimeout);
  }
  
  // Chỉ update UI text ngay lập tức, không load data
  const d = new Date(time);
  if (d) {
    timeTextDiv.innerText = d.toString();
  }
  
  // Đặt timeout mới - chỉ load data khi dừng kéo > 1 giây
  timeSliderTimeout = setTimeout(() => {
    console.log('Loading data for time:', new Date(time).toString());
    layer.setAnimationTime(time / 1000);

    if (temperatureControlsManager) {
        const tempLayer = temperatureControlsManager.getTemperatureLayer();
        if (tempLayer) tempLayer.setAnimationTime(time / 1000);
    }

    if (windMapControlsManager) {
        const windMapLayer = windMapControlsManager.getWindMapLayer();
        if (windMapLayer) windMapLayer.setAnimationTime(time / 1000);
    }
    
    // Update pointer data với thời gian mới
    updatePointerValue(pointerLngLat);
    
    timeSliderTimeout = null;
  }, 1000); // Đợi 1 giây sau khi dừng kéo (timeline loading)
});

playPauseButton.addEventListener("click", () => {
  isPlaying = !isPlaying;
  if (isPlaying) {
    layer.animateByFactor(3600); 
    if (temperatureControlsManager) {
        const tempLayer = temperatureControlsManager.getTemperatureLayer();
        if (tempLayer) tempLayer.animateByFactor(3600);
    }
    if (windMapControlsManager) {
        const windMapLayer = windMapControlsManager.getWindMapLayer();
        if (windMapLayer) windMapLayer.animateByFactor(3600);
    }
    playPauseButton.innerText = "Pause";
  } else {
    layer.animateByFactor(0); 
    if (temperatureControlsManager) {
        const tempLayer = temperatureControlsManager.getTemperatureLayer();
        if (tempLayer) tempLayer.animateByFactor(0);
    }
    if (windMapControlsManager) {
        const windMapLayer = windMapControlsManager.getWindMapLayer();
        if (windMapLayer) windMapLayer.animateByFactor(0);
    }
    playPauseButton.innerText = "Play 3600x";
  }
});



hillshadeToggleButton.addEventListener('click', function() {
  hillshadeVisible = !hillshadeVisible;
  const visibility = hillshadeVisible ? 'visible' : 'none';
  if (map.getLayer('hillshade-shadows')) { 
    map.setLayoutProperty('hillshade-shadows', 'visibility', visibility); 
  }
  if (map.getLayer('hillshade-highlights')) { 
    map.setLayoutProperty('hillshade-highlights', 'visibility', visibility); 
  }
  hillshadeToggleButton.innerText = hillshadeVisible ? 'Ẩn bóng địa hình' : 'Hiện bóng địa hình';
});

// Lắng nghe sự kiện thay đổi projection từ icon có sẵn
let currentProjection = 'mercator';

// Wind layer parameters for different projections
const windProjectionSettings = {
  'mercator': {
    speed: 0.002,       // ✅ KHÔI PHỤC settings gốc của bạn
    fadeFactor: 0.03,   // ✅ Giữ nguyên
    maxAmount: 256,     // ✅ Giữ nguyên
    density: 400        // ✅ KHÔI PHỤC settings gốc của bạn
  },
  'globe': {
    speed: 0.0008,      // ✅ Tăng lên một chút để wind patterns rõ hơn
    fadeFactor: 0.04,   // ✅ Giảm fade để particles tồn tại lâu hơn, tạo patterns rõ hơn
    maxAmount: 180,     // ✅ Tăng particles để thấy được wind patterns
    density: 150,       // ✅ Tăng density để wind flow liên tục hơn
    // ✅ Thêm settings đặc biệt cho globe
    refreshInterval: 150, // Refresh chậm hơn để ổn định
    size: 1.2           // Particles lớn hơn một chút để dễ thấy trên sphere
  },
  'equalEarth': {
    speed: 0.0015,      // ✅ Trung gian
    fadeFactor: 0.04,   // ✅ Trung gian
    maxAmount: 200,     // ✅ Trung gian
    density: 250        // ✅ Trung gian
  }
};

// Function to adjust wind layer for projection
function adjustWindLayerForProjection(projection) {
  if (!layer || !map.getLayer('Wind Particles')) {
    console.log('⚠️ Wind layer not ready for projection adjustment');
    return;
  }

  const settings = windProjectionSettings[projection] || windProjectionSettings['mercator'];
  console.log(`🌪️ Adjusting wind layer for projection: ${projection}`, settings);

  try {
    // Store current state
    const isPlaying = layer.isPlaying();
    const currentTime = layer.getAnimationTime();
    const wasVisible = map.getLayoutProperty('Wind Particles', 'visibility') === 'visible';

    // Get current colors from wind controls
    let currentColors = { color: [255, 0, 0, 30], fastColor: [255, 255, 255, 100] };
    if (window.windControlsManager) {
      const windSettings = window.windControlsManager.getCurrentSettings();
      if (windSettings.slowColor) currentColors.color = windSettings.slowColor;
      if (windSettings.fastColor) currentColors.fastColor = windSettings.fastColor;
    }

    // Remove current layer
    map.removeLayer('Wind Particles');

    // Create new layer with projection-specific settings
    const layerConfig = {
      id: "Wind Particles",
      colorramp: maptilerweather.ColorRamp.builtin.NULL,
      speed: settings.speed,
      fadeFactor: settings.fadeFactor,
      maxAmount: settings.maxAmount,
      density: settings.density,
      color: currentColors.color,
      fastColor: currentColors.fastColor,
    };

    // Add globe-specific settings if available
    if (settings.refreshInterval) layerConfig.refreshInterval = settings.refreshInterval;
    if (settings.size) layerConfig.size = settings.size;

    layer = new maptilerweather.WindLayer(layerConfig);

    // Add new layer to map
    map.addLayer(layer);

    // Restore state when layer is ready
    layer.on("sourceReady", () => {
      if (currentTime) {
        layer.setAnimationTime(currentTime);
      }
      if (isPlaying) {
        layer.play();
      }
      if (!wasVisible) {
        map.setLayoutProperty('Wind Particles', 'visibility', 'none');
      }

      // Setup events for new layer
      setupWindLayerEvents();

      console.log(`✅ Wind layer adjusted for ${projection} projection`);
    });

  } catch (e) {
    console.error('❌ Error adjusting wind layer for projection:', e);
  }
}

// Theo dõi thay đổi projection
setInterval(() => {
  const newProjection = map.getProjection();
  if (newProjection !== currentProjection) {
    console.log(`🗺️ Projection changed: ${currentProjection} → ${newProjection}`);
    currentProjection = newProjection;

    // Adjust wind layer for new projection
    if (layer && map.getLayer('Wind Particles')) {
      setTimeout(() => {
        adjustWindLayerForProjection(newProjection);
      }, 500); // Delay để đảm bảo projection đã được apply
    }

    if (newProjection && newProjection !== 'mercator') {
      // Hiện notification khi chuyển sang projection khác mercator (như globe)
      projectionNotification.style.display = 'block';
      setTimeout(() => {
        projectionNotification.style.display = 'none';
      }, 5000); // Tự tắt sau 5 giây
    }
  }
}, 100); // Kiểm tra mỗi 100ms

// Mouse tracking for tooltip
let isMouseOverMap = false;

map.on('mousemove', (e) => {
  isMouseOverMap = true;
  updatePointerValue(e.lngLat);
  
  // Update tooltip position
  const point = map.project(e.lngLat);
  pointerDataDiv.style.left = (point.x + 15) + 'px'; // Offset 15px from cursor
  pointerDataDiv.style.top = (point.y - 35) + 'px';  // Offset 35px above cursor
  pointerDataDiv.style.display = 'block';
});

// Hide tooltip when mouse leaves map
map.on('mouseleave', () => {
  isMouseOverMap = false;
  pointerDataDiv.style.display = 'none';
});

// Hide tooltip when map starts moving/dragging
map.on('movestart', () => {
  pointerDataDiv.style.display = 'none';
});

map.on('dragstart', () => {
  pointerDataDiv.style.display = 'none';
});

// Hide tooltip during zoom
map.on('zoomstart', () => {
  pointerDataDiv.style.display = 'none';
});

// Ensure tooltip stays hidden after map movement if mouse not over map
map.on('moveend', () => {
  if (!isMouseOverMap) {
    pointerDataDiv.style.display = 'none';
  }
});

map.on('zoomend', () => {
  if (!isMouseOverMap) {
    pointerDataDiv.style.display = 'none';
  }
});

// Hide tooltip when hovering over UI elements
const uiElements = [
  timeInfoContainer,
  document.getElementById('scene-controls'),
  document.getElementById('color-scale-container'),
  document.getElementById('location-panel'),
  // Wind controls now handled by JS
  document.querySelector('.maplibregl-ctrl-top-right'), // Geocoding control
  document.querySelector('.maplibregl-ctrl-top-left'),  // Other controls
  document.querySelector('.maplibregl-ctrl-bottom-right'), // Attribution
  document.getElementById('projection-notification'),
  document.getElementById('location-popup')
];

uiElements.forEach(element => {
  if (element) {
    element.addEventListener("mouseenter", () => {
      pointerDataDiv.style.display = 'none';
    });
    
    element.addEventListener("mouseleave", () => {
      // Only show tooltip if mouse is still over map and not moving
      if (pointerLngLat && isMouseOverMap) {
        setTimeout(() => {
          if (isMouseOverMap) {
            pointerDataDiv.style.display = 'block';
          }
        }, 100);
      }
    });
  }
});

// === MANAGERS INITIALIZATION ===
// Initialize all managers after map loads
map.on('load', () => {
    console.log('🗺️ Map loaded, initializing managers...');

    // Initialize Location Manager
    if (window.locationManager) {
        window.locationManager.initializeLocationPanel();
        console.log('📍 Location manager initialized');
    }

    // Initialize Border Manager
    if (window.borderManager) {
        window.borderManager.initializeBorderManager();
        console.log('🗺️ Border manager initialized');
    }

    // Initialize Scene Manager
    if (window.offlineSceneManager) {
        window.offlineSceneManager.init({
            map: map,
            timeSlider: timeSlider,
            layer: layer,
            // ✅ Thay bằng reference từ temperatureControlsManager:
            layerBg: () => temperatureControlsManager.getTemperatureLayer(),
            tempLayerVisible: () => temperatureControlsManager.currentSettings.visible,
            toggleTempButton: null, // Không cần nữa vì dùng UI mới

            // ✅ Wind map layer references:
            windMapLayer: () => windMapControlsManager.getWindMapLayer(),
            windMapVisible: () => windMapControlsManager.currentSettings.visible,

            hillshadeVisible: () => hillshadeVisible,
            isPlaying: () => isPlaying,
            hillshadeToggleButton: hillshadeToggleButton,
            playPauseButton: playPauseButton,
            refreshTimeUI: refreshTimeUI
        });

        // Load saved scenes
        window.offlineSceneManager.loadSavedScenes();
        console.log('🎬 Scene manager initialized');
    }
});